import { on<PERSON><PERSON>, HttpsError, onRequest } from 'firebase-functions/v2/https'
import {
  onDocumentCreated,
  onDocumentUpdated,
} from 'firebase-functions/v2/firestore'
import { logger } from 'firebase-functions/v2'
import { initializeApp } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import axios from 'axios'

type Venue = {
  id: string
  address: {
    address1?: string
    address2?: string
    town?: string
    county?: string
    postcode?: string
  }
  [key: string]: any // For other venue properties
}

type Event = {
  id: string
  acts: string[]
  when?: Timestamp
  venue: string | Venue
  notes?: any
  isPrivate?: boolean
  addressString?: string
  [key: string]: any // For other event properties
}

// Initialize Firebase Admin
initializeApp()

export const onTaskCreated = onDocumentCreated('tasks/{taskId}', async task => {
  const snapshot = task.data
  if (!snapshot) {
    console.log('No data found for task:', task.id)
    return
  }
  const taskData = snapshot.data()
  console.log('Task created:', taskData)

  try {
    await axios.post('https://n8n.davesroyorbison.com/webhook/new-task', {
      ...taskData,
      taskId: task.id,
    })
    logger.info('Task sent to n8n')
  } catch (error: any) {
    logger.error('Error sending task to n8n:', error?.message || error)
  }
})

export const onTaskUpdated = onDocumentUpdated('tasks/{taskId}', async task => {
  logger.info('Task updated:', task.id)

  const newValue = task.data?.after.data()
  if (!newValue) {
    logger.error('No new data found for task:', task.id)
    return
  }

  const oldValue = task.data?.before.data()
  if (!oldValue) {
    logger.error('No old data found for task:', task.id)
    return
  }

  // Check if task has been completed
  if (newValue.isCompleted && !oldValue.isCompleted) {
    logger.info('Task completed:', newValue)

    const updatedTask = {
      ...newValue,
      taskId: task.id,
    }

    logger.info('Sending completed task to n8n:', updatedTask)

    try {
      await axios.post(
        'https://n8n.davesroyorbison.com/webhook/task-completed',
        updatedTask,
      )
      logger.info('Completed task sent to n8n')
      return
    } catch (error: any) {
      logger.error(
        'Error sending completed task to n8n:',
        error?.message || error,
      )
    }
  }

  // Check if task urgency or due date has changed
  if (newValue.isUrgent !== oldValue.isUrgent) {
    logger.info('Task urgency changed:', newValue)

    try {
      await axios.post(
        'https://n8n.davesroyorbison.com/webhook/task-urgency-changed',
        {
          ...newValue,
          taskId: task.id,
          previousUrgency: oldValue.isUrgent,
        },
      )
      logger.info('Task urgency change sent to n8n')
    } catch (error: any) {
      logger.error(
        'Error sending task urgency change to n8n:',
        error?.message || error,
      )
    }
  }

  // Check if task due date has changed
  if (newValue.dueDate?.seconds !== oldValue.dueDate?.seconds) {
    logger.info('Task due date changed:', newValue)

    try {
      await axios.post(
        'https://n8n.davesroyorbison.com/webhook/task-due-date-changed',
        {
          ...newValue,
          taskId: task.id,
          previousDueDate: oldValue.dueDate
            ? oldValue.dueDate.toDate().toISOString()
            : null,
        },
      )
      logger.info('Task due date change sent to n8n')
    } catch (error: any) {
      logger.error(
        'Error sending task due date change to n8n:',
        error?.message || error,
      )
    }
  }
})

export const onEventCreated = onDocumentCreated(
  'events/{eventId}',
  async event => {
    async function venueAddress(venueId: string): Promise<string> {
      // Function to return the venue's address details
      if (!venueId) return 'A venue was not provided'

      try {
        const db = getFirestore()
        const venueRef = db.collection('venues').doc(venueId)
        const venueSnapshot = await venueRef.get()

        if (venueSnapshot.exists) {
          const venueData = venueSnapshot.data() as Venue
          // return address fields as a single string joined by ', '
          return [
            venueData.name,
            venueData.address?.address1,
            venueData.address?.address2,
            venueData.address?.town,
            venueData.address?.county,
            venueData.address?.postcode,
          ]
            .filter(Boolean)
            .join(', ')
        }
      } catch (error) {
        console.error('Error fetching venue address:', error)
      }

      return 'Venue not found'
    }

    const snapshot = event.data
    if (!snapshot) {
      console.log('No data found for event:', event.id)
      return
    }
    const eventData = snapshot.data()
    console.log('Event created:', eventData)

    // Fetch venue details
    eventData.venueAddress = await venueAddress(eventData.venue)

    try {
      await axios.post('https://n8n.davesroyorbison.com/webhook/new-event', {
        ...eventData,
        eventId: event.id,
      })
      logger.info('Event sent to n8n')
    } catch (error: any) {
      logger.error('Error sending event to n8n:', error?.message || error)
    }
  },
)

// Function to compare user details between Auth and Firestore
export const compareUserDetails = onCall(
  {
    region: 'us-central1',
  },
  async request => {
    // Check if the caller is authenticated
    if (!request.auth) {
      throw new HttpsError(
        'unauthenticated',
        'User must be logged in to perform this action',
      )
    }

    // Get the target user ID from the request
    const targetUserId = request.data.userId
    if (!targetUserId) {
      throw new HttpsError('invalid-argument', 'User ID is required')
    }

    try {
      // Get Auth user details
      const auth = getAuth()
      const authUser = await auth.getUser(targetUserId)

      // Get Firestore user details
      const db = getFirestore()
      const userDoc = await db.collection('users').doc(targetUserId).get()

      if (!userDoc.exists) {
        throw new HttpsError(
          'not-found',
          'User document not found in Firestore',
        )
      }

      const firestoreUser = userDoc.data()

      // Compare names
      const authName = authUser.displayName || ''
      const [authFirstName = '', ...authLastNameParts] = authName.split(' ')
      const authLastName = authLastNameParts.join(' ')

      const firestoreFirstName = firestoreUser?.firstName || ''
      const firestoreLastName = firestoreUser?.lastName || ''

      const nameComparison = {
        auth: {
          displayName: authName,
          firstName: authFirstName,
          lastName: authLastName,
        },
        firestore: {
          firstName: firestoreFirstName,
          lastName: firestoreLastName,
        },
        matches: {
          firstName:
            authFirstName.toLowerCase() === firestoreFirstName.toLowerCase(),
          lastName:
            authLastName.toLowerCase() === firestoreLastName.toLowerCase(),
        },
      }

      return nameComparison
    } catch (error) {
      console.error('Error comparing user details:', error)
      throw new HttpsError('internal', 'Failed to compare user details')
    }
  },
)

export const upcomingEvents = onRequest({ cors: true }, async (req, res) => {
  const act = req.query.act as string

  if (!act) {
    res.status(400).send('Missing act')
    return
  }

  try {
    const db = getFirestore()
    const today = new Date()

    // Fetch events first
    const eventsSnapshot = await db
      .collection('events')
      .where('acts', 'array-contains', act)
      .where('when', '>=', Timestamp.fromDate(today))
      .get()

    // Fetch venues
    const venuesSnapshot = await db.collection('venues').get()
    const venues = venuesSnapshot.docs.map(doc => ({
      ...doc.data(),
      id: doc.id,
    })) as Venue[]

    const events = eventsSnapshot.docs.map(doc => {
      const eventData = doc.data() as Event
      const event: Event = {
        ...eventData,
        id: doc.id,
      }

      // Convert Timestamp to Date
      event.date = event.when?.toDate()
      delete event.when

      // Ensure isPrivate is defined
      event.isPrivate = event.isPrivate || false

      // Replace venue ID with venue object
      const venue =
        venues.find(venue => venue.id === event.venue) || event.venue

      event.venue = venue

      // Build the address
      let addressString = [
        typeof venue === 'object' ? venue.address?.address1 : null,
        typeof venue === 'object' ? venue.address?.address2 : null,
        typeof venue === 'object' ? venue.address?.town : null,
        typeof venue === 'object' ? venue.address?.county : null,
      ]
        .filter(Boolean)
        .join(', ')

      if (addressString) {
        addressString +=
          ' ' + (typeof venue === 'object' ? venue.address?.postcode : null)
      }

      event.addressString = addressString || ''

      // Remove sensitive information
      delete event.notes
      if (typeof event.venue === 'object') {
        delete event.venue.notes
      }

      return event
    })

    if (events.length > 0) {
      res.json(events)
    } else {
      res.status(404).send('No events found')
    }
  } catch (error) {
    console.error('Error fetching upcoming events:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})
