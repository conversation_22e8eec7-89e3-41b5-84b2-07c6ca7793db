{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["firstName", "lastName", "stageName"], "properties": {"id": {"type": "string", "description": "Unique identifier for the artist"}, "firstName": {"type": "string", "description": "Artist's first name"}, "lastName": {"type": "string", "description": "Artist's last name"}, "stageName": {"type": "string", "description": "Artist's stage name"}, "instruments": {"type": "array", "items": {"type": "string"}, "description": "List of instruments the artist plays"}, "photoId": {"type": "string", "description": "Public ID for artist's main photo"}}}