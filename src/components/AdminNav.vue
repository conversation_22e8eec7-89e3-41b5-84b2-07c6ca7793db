<script setup lang="ts">
import { useFirebase } from '@/composables/firebase/useFirebase'

const { currentUser } = useFirebase()
</script>

<template>
  <nav v-if="currentUser" class="admin-nav">
    <RouterLink :to="{ name: 'events.index' }" class="admin-nav__link" active-class="router-link-active">Events
    </RouterLink>
    <RouterLink :to="{ name: 'calendar' }" class="admin-nav__link" active-class="router-link-active">Calendar
    </RouterLink>
    <RouterLink :to="{ name: 'acts.index' }" class="admin-nav__link" active-class="router-link-active">Acts</RouterLink>
    <RouterLink :to="{ name: 'venues.index' }" class="admin-nav__link" active-class="router-link-active">Venues
    </RouterLink>
    <RouterLink :to="{ name: 'artists.index' }" class="admin-nav__link" active-class="router-link-active">Artists
    </RouterLink>
  </nav>
</template>

<style scoped>
.admin-nav {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  justify-content: center;
  margin-bottom: 2rem;
}

.admin-nav__link {
  color: var(--color-text);
  text-decoration: none;
  padding: 0.5em 1em;
  border-radius: 0.5em;
  transition: all 0.2s ease;
}

.admin-nav__link:hover {
  background-color: var(--color-background-mute);
  color: var(--color-accent);
}

.admin-nav__link.router-link-active {
  background-color: var(--color-accent);
  color: var(--color-background);
}
</style>
