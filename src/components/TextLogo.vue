<script setup lang="ts">
type Props = {
  publicId: string
  alt: string
  height?: string | number
  width?: string | number
}

withDefaults(defineProps<Props>(), {
  height: 60,
  width: 'auto'
})
</script>

<template>
  <CloudImage :key="publicId" :publicId="publicId" :alt="alt" :height="height" :width="width" class="text-logo" />
</template>

<style scoped>
.text-logo {
  filter: drop-shadow(var(--filter-shadow));
}
</style>
