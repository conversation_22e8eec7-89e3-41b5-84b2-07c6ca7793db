<script setup lang="ts">
import CalendarDay from '@/components/calendar/CalendarDay.vue'
import { Artist } from '@/models/Artist'
import type { Day } from '@/composables/calendar/useCalendar'

type Week = Day[]

type Props = {
  weeks: Week[]
  selectedDates: Date[]
}

defineProps<Props>()

const emit = defineEmits<{
  (e: 'select-day', day: Day, event: MouseEvent): void
  (e: 'unavailable-click', day: Day, artist: Artist): void
}>()
</script>

<template>
  <div class="calendar__grid">
    <div class="calendar__weekdays">
      <div v-for="day in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" :key="day" class="calendar__weekday">
        {{ day }}
      </div>
    </div>

    <div class="calendar__weeks">
      <div v-for="(week, weekIndex) in weeks" :key="weekIndex" class="calendar__week">
        <CalendarDay v-for="(day, dayIndex) in week" :key="`${weekIndex}-${dayIndex}`" :day="day"
          :initial-day="dayIndex === 0" :is-past="false" :selected-dates="selectedDates"
          @select="(day, $event) => emit('select-day', day, $event)"
          @unavailable-click="(day, artist) => emit('unavailable-click', day, artist)" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.calendar__grid {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  overflow: hidden;
}

.calendar__weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: var(--color-background-alt);
  border-bottom: 1px solid var(--color-border);
}

.calendar__weekday {
  padding: 0.5rem;
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
}

.calendar__weeks {
  display: flex;
  flex-direction: column;
}

.calendar__week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid var(--color-border);
}

.calendar__week:last-child {
  border-bottom: none;
}
</style>
