<script setup lang="ts">
import { computed, ref } from 'vue'
import { useArtistAvailability } from '@/composables/artists/useArtistAvailability'
import { useArtists } from '@/composables/artists/useArtists'
import { useMonthColor } from '@/composables/useMonthColor'
import EventStrip from '@/components/events/EventStrip.vue'
import ArtistAvatar from '@/components/artists/ArtistAvatar.vue'
import { Artist } from '@/models/Artist'
import { Event } from '@/models/Event'
import { X, Ellipsis } from 'lucide-vue-next'
import type { Day } from '@/composables/calendar/useCalendar'

const props = defineProps<{
  day: Day
  initialDay: boolean
  isPast: boolean
  selectedDates: Date[]
}>()

const { monthColor, textColor } = useMonthColor()

const { unavailableDates } = useArtistAvailability()
const { artists } = useArtists()

const isDayMenuOpen = ref(false)
function handleDayMenuClick(event: MouseEvent) {
  event.stopPropagation()
  toggleDayMenu()
}
function closeDayMenu() {
  isDayMenuOpen.value = false
}
function toggleDayMenu() {
  isDayMenuOpen.value = !isDayMenuOpen.value
}

const isToday = computed(() => {
  const today = new Date()
  return props.day.date.toDateString() === today.toDateString()
})

const currentYear = computed(() => new Date().getFullYear())

const monthDisplay = computed(() => {
  const date = props.day.date
  const monthStr = date.toLocaleString('en-US', { month: 'short' })
  const isOtherYear = date.getFullYear() !== currentYear.value
  return isOtherYear ? `${monthStr} ${date.getFullYear().toString().slice(-2)}` : monthStr
})

const isPast = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return props.day.date < today
})

const isSelected = computed(() => {
  return props.selectedDates.some(date => date.toDateString() === props.day.date.toDateString())
})

type UnavailableArtist = Omit<Artist, 'id'> & {
  id: string
  unavailabilityReason: string
}

// Get unavailable artists for this day
const unavailableArtists = computed<UnavailableArtist[]>(() => {
  if (!unavailableDates.value?.length) return []

  const checkDate = props.day.date
  checkDate.setHours(12, 0, 0, 0)

  // Get unavailabilities for this date
  const dayUnavailabilities = unavailableDates.value.filter(unavailable => {
    if (!unavailable.startDate || !unavailable.endDate) return false
    const start = new Date(unavailable.startDate)
    const end = new Date(unavailable.endDate)
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 999)

    return checkDate >= start && checkDate <= end
  })

  // Map to artist details
  return dayUnavailabilities.map(unavailable => {
    const artist = artists.value.find(a => a.id === unavailable.artistId)
    if (!artist || !artist.id) return null
    return {
      ...artist,
      id: artist.id,
      unavailabilityReason: unavailable.reason || 'Unavailable'
    }
  }).filter((artist): artist is UnavailableArtist => artist !== null)
})

// Compute background color based on month and selection state
const computedStyles = computed(() => {
  const styles: { backgroundColor: string; color?: string } = {
    backgroundColor: monthColor(props.day.month, isSelected.value),
  }

  if (isSelected.value) {
    const selectedTextColor = textColor(props.day.month, isSelected.value)
    if (selectedTextColor) {
      styles.color = selectedTextColor
    }
  }

  return styles
})

const emit = defineEmits<{
  (e: 'select', day: Day, event: MouseEvent): void
  (e: 'unavailable-click', day: Day, artist: UnavailableArtist): void
}>()

function handleDayClick(event: MouseEvent) {
  emit('select', props.day, event)
}

function handleUnavailableClick(artist: UnavailableArtist, event: MouseEvent) {
  event.stopPropagation()
  emit('unavailable-click', props.day, artist)
  closeDayMenu()
}

function handleMarkUnavailableClick() {
  // TODO Implement the logic to mark an artist as unavailable
  console.log('Mark Unavailable clicked for', props.day.date)
}

function isAfterSix(): boolean {
  // Return false if the current day is today and the time is after 6pm
  return isToday.value && new Date().getHours() >= 18
}

</script>

<template>
  <div class="calendar-day" :class="[
    {
      'is-today': isToday,
      'is-past': isPast,
      'has-events': day.gigDetails.length,
      'has-unavailable': unavailableArtists.length,
      'is-selected': isSelected
    }
  ]" :style="computedStyles" @click="handleDayClick">
    <div class="date" :class="{ 'is-today': isToday }">
      <div class="day-indicator">
        {{ day.date.getDate() }}
      </div>
      <div v-if="day.date.getDate() === 1 || initialDay" class="month-indicator">
        {{ monthDisplay }}
      </div>
    </div>

    <div v-if="!isPast" class="calendar-day--menu-trigger" title="Menu" @click="handleDayMenuClick">
      <X v-if="isDayMenuOpen" class="icon" />
      <Ellipsis v-else class="icon" />
    </div>

    <dialog :open="isDayMenuOpen" class="calendar-day--menu" @click="handleDayMenuClick"
      @mouseleave="isDayMenuOpen = false">
      <RouterLink v-if="!isPast && !isAfterSix()"
        :to="{ name: 'events.create', query: { date: day.date.toISOString().split('T')[0] } }"
        class="base-button level-0">
        Create Event
      </RouterLink>
      <!-- Mark an artist as unavailable using a popup dialog -->
      <BaseButton v-if="!isPast" @click="handleMarkUnavailableClick" variant="outline" size="tiny">Mark Unavailable
      </BaseButton>
    </dialog>

    <div class="events-container" v-if="day.gigDetails.length">
      <EventStrip v-for="gig in day.gigDetails" :key="gig.id" :event="gig as Event" />
    </div>

    <div v-if="unavailableArtists.length" class="unavailable-artists" :class="{ 'is-past': isPast }">
      <div v-for="artist in unavailableArtists" :key="artist.id" class="unavailable-artist">
        <ArtistAvatar :artist="artist" :title="artist.unavailabilityReason" size="x-small"
          @click="handleUnavailableClick(artist, $event)" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.calendar-day {
  position: relative;
  min-height: 6rem;
  padding: 0.75rem;
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.2s ease;

  .calendar-day--menu-trigger {
    position: absolute;
    font-size: var(--step--1);
    top: .6em;
    right: .5em;
    background-color: transparent;
    display: grid;
    width: 1.5em;
    aspect-ratio: 1;
    place-items: center;
    border: 1px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: color var(--transition-in), background-color var(--transition-in), border-color var(--transition-in);

    @media (hover: hover) {
      color: transparent;

      &:hover {
        transition: color var(--transition-out), background-color var(--transition-out), border-color var(--transition-out);
        border-color: var(--color-text);
      }
    }
  }

  &:hover {
    .calendar-day--menu-trigger {
      transition: color var(--transition-out), background-color var(--transition-out), border-color var(--transition-out);
      color: var(--color-text);
      background-color: var(--color-brand-alpha);
    }
  }
}

.calendar-day:last-child {
  border-right: none;
}

.calendar-day.is-past {
  opacity: 0.7;
}

.calendar-day.is-past .day-indicator {
  opacity: 0.7;
}

.date {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.day-indicator {
  font-size: 0.875rem;
  font-weight: 500;
  user-select: none;
}

.month-indicator {
  font-size: 0.75rem;
  color: var(--color-text-muted);
  user-select: none;
}

.calendar-day--menu {
  font-size: var(--step--1);
  border: none;
  padding: var(--space-3xs) var(--space-xs);
  border-radius: var(--radius-s);
  border: 1px solid var(--color-text-soft);
}

.is-today .day-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.6rem;
  height: 1.6rem;
  background: var(--color-brand-alpha);
  color: var(--color-text);
  outline: 1px solid var(--color-text);
  border-radius: 50%;
  line-height: 1;
}

.events-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: auto;
}

.unavailable-artists {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
}

.unavailable-artist {
  cursor: pointer;
  user-select: none;
}

.selected-artist {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: var(--color-background);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
}

.calendar-day.is-selected {
  position: relative;
  z-index: 1;
}

.calendar-day.is-selected .month-indicator {
  color: inherit;
  opacity: 0.9;
}

.calendar-day.is-selected .events-container {
  color: inherit;
}

.calendar-day.is-selected.is-past {
  opacity: 0.9;
}

/* Adjust unavailable artists section for selected state */
.calendar-day.is-selected .unavailable-artists {
  background-color: rgba(var(--color-background-rgb), 0.1);
  border-radius: var(--radius-sm);
  padding: 0.25rem;
}
</style>
