<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from 'vue'
import { useEvents } from '@/composables/events/useEvents'
import { useMonthColor } from '@/composables/useMonthColor'
import { ArrowLeft, ArrowRight } from 'lucide-vue-next'

type Month = {
  index: number
  name: string
}

const props = defineProps({
  allMonths: {
    type: Array as () => Month[],
    required: true
  },
  currentTime: {
    type: Date,
    required: true
  },
  hasOlderEvents: {
    type: Boolean,
    required: true
  }
})

const { subscribeToEvents, getEventsDateRange } = useEvents()
const { monthColor, textColor } = useMonthColor()

const currentMonth = computed(() => props.currentTime.getMonth())
const currentYear = computed(() => props.currentTime.getFullYear())

const emit = defineEmits<{
  (e: 'navigate-month', month: number, year: number): void
  (e: 'toggle-size'): void
  (e: 'reset-today'): void
  (e: 'move-backward'): void
  (e: 'move-forward'): void
}>()

const yearOptions = ref<Array<{ value: string, label: string }>>([])

watchEffect(async () => {
  const dateRange = await getEventsDateRange()

  const minYear = dateRange?.years()[0]
  const maxYear = new Date().getFullYear() + 2

  yearOptions.value = Array.from({ length: maxYear - minYear + 1 }, (_, i) => minYear + i).map(year => ({
    value: year.toString(),
    label: year.toString()
  }))
})

const buttonStyles = computed(() => (monthIndex: number) => {
  const isActive = currentMonth.value === monthIndex
  const backgroundColor = monthColor(monthIndex, isActive)
  const activeTextColor = textColor(monthIndex, isActive)
  return {
    backgroundColor,
    color: isActive ? activeTextColor : 'var(--color-text)',
    borderColor: isActive ? backgroundColor : 'transparent'
  }
})

function handleYearChange(value: string | number | null): void {
  if (value === null) return
  const yearValue = typeof value === 'string' ? parseInt(value, 10) : value
  emit('navigate-month', currentMonth.value, yearValue)
}

function handleMonthClick(monthIndex: number): void {
  emit('navigate-month', monthIndex, currentYear.value)
}

onMounted(async () => {
  const fromDate = new Date(currentYear.value, currentMonth.value, 1)
  await subscribeToEvents({ fromDate })
})
</script>

<template>
  <div class="calendar__header">
    <div class="calendar__legend">
      <BaseSelect class="year-select" :model-value="currentYear.toString()" :options="yearOptions"
        @update:model-value="handleYearChange" :clearable="false" id="calendar-header-year-select" />
      <div class="month-buttons">
        <BaseButton v-for="month in allMonths" :key="month.index" :class="{ active: currentMonth === month.index }"
          :style="buttonStyles(month.index)" @click="handleMonthClick(month.index)" size="compact">
          {{ month.name.slice(0, 3) }}
        </BaseButton>
      </div>
    </div>

    <div class="calendar__actions">
      <BaseButton class="nav-button" :disabled="!hasOlderEvents" @click="emit('move-backward')"
        :class="{ 'nav-button--disabled': !hasOlderEvents }" size="tiny" purpose="primary">
        <ArrowLeft />
      </BaseButton>
      <BaseButton size="tiny" @click="emit('reset-today')" purpose="primary">
        Today
      </BaseButton>
      <BaseButton class="nav-button" @click="emit('move-forward')" size="tiny" purpose="primary">
        <ArrowRight />
      </BaseButton>
    </div>
  </div>
</template>

<style scoped>
.calendar__header {
  container-name: header;
  container-type: inline-size;
  display: grid;
  gap: var(--space-gap-xs);
  flex-wrap: wrap;
  justify-content: center;
  justify-items: center;
  margin-bottom: 1rem;
}

.calendar__legend {
  display: grid;
  gap: 0.5rem;
  margin-inline: auto;
}

.month-buttons {
  display: grid;
  gap: 0.25rem;
  padding: 0.25rem;
  width: 100%;
  margin-inline: auto;

  /* Default: 3 buttons wide for smallest containers */
  grid-template-columns: repeat(3, 1fr);

  >.active {
    font-style: italic;
  }

  /* 4 buttons wide */
  @container header (min-width: 20rem) {
    grid-template-columns: repeat(4, 1fr);
  }

  /* 6 buttons wide */
  @container header (min-width: 30rem) {
    grid-template-columns: repeat(6, 1fr);
  }

  /* 12 buttons wide - full width */
  @container header (min-width: 48rem) {
    grid-template-columns: repeat(12, 1fr);
  }
}

.year-select {
  width: fit-content;
  margin-inline: auto;
}

.calendar__actions {
  display: grid;
  grid-template-columns: auto auto auto;
  gap: 0.5rem;
}

.nav-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
