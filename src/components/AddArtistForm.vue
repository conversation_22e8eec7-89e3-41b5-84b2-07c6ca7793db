<script setup lang="ts">
import { ref } from 'vue'

type ArtistForm = {
  firstName: string
  lastName: string
  stageName: string
  instruments: string
  photoFile: File | null
}

const emit = defineEmits<{
  close: []
}>()

const form = ref<ArtistForm>({
  firstName: '',
  lastName: '',
  stageName: '',
  instruments: '',
  photoFile: null
})

const photoPreview = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)

const handlePhotoChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    form.value.photoFile = file
    photoPreview.value = URL.createObjectURL(file)
  }
}

const generateStageName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`.trim()
}

const uploadToCloudinary = async (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('upload_preset', 'artist-photos')

  const response = await fetch(
    'https://api.cloudinary.com/v1_1/dave-collison/image/upload',
    {
      method: 'POST',
      body: formData
    }
  )

  if (!response.ok) {
    throw new Error('Failed to upload image')
  }

  return await response.json()
}

const handleSubmit = async () => {
  isSubmitting.value = true
  submitError.value = null

  try {
    if (!form.value.photoFile) {
      throw new Error('Please select a photo')
    }

    const uploadResult = await uploadToCloudinary(form.value.photoFile)

    // TODO: Save artist data to database
    console.log('Form submitted:', {
      ...form.value,
      photoId: uploadResult.secure_url
    })

    emit('close')
  } catch (error) {
    submitError.value = error instanceof Error ? error.message : 'An error occurred'
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <div class="add-artist-form" @click.stop>
    <h3>Add New Artist</h3>

    <form @submit.prevent="handleSubmit">
      <div class="form-group photo-upload">
        <label for="photo">Profile Photo</label>
        <div class="photo-preview" :class="{ 'has-photo': photoPreview }">
          <img v-if="photoPreview" :src="photoPreview" alt="Photo preview" />
          <span v-else class="photo-placeholder">Click to add photo</span>
          <input type="file" id="photo" accept="image/*" @change="handlePhotoChange" class="photo-input" />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input type="text" id="firstName" v-model="form.firstName" required />
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input type="text" id="lastName" v-model="form.lastName" required />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="stageName">Stage Name (optional)</label>
          <input type="text" id="stageName" v-model="form.stageName"
            :placeholder="generateStageName(form.firstName, form.lastName)" />
        </div>

        <div class="form-group">
          <label for="instruments">Instruments (comma-separated)</label>
          <input type="text" id="instruments" v-model="form.instruments" placeholder="e.g. guitar, vocals, piano"
            required />
        </div>
      </div>

      <div class="form-actions">
        <BaseButton type="button" variant="ghost" @click="emit('close')">
          Cancel
        </BaseButton>
        <BaseButton type="submit" class="submit-button" :disabled="isSubmitting">
          <span v-if="isSubmitting" class="loading-spinner"></span>
          {{ isSubmitting ? 'Adding Artist...' : 'Add Artist' }}
        </BaseButton>
      </div>

      <p v-if="submitError" class="error-message">
        {{ submitError }}
      </p>
    </form>
  </div>
</template>

<style scoped>
.add-artist-form {
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 2rem;
  width: 90%;
  max-width: 800px;
  box-shadow: var(--shadow);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.photo-upload {
  text-align: center;
  margin-bottom: 2rem;
}

.photo-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 2px dashed var(--color-border);
  margin: 1rem auto;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: var(--color-background);
  transition: all 0.2s ease;
}

.photo-preview:hover {
  border-color: var(--color-accent);
}

.photo-preview.has-photo {
  border-style: solid;
}

.photo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-placeholder {
  color: var(--color-text-muted);
}

.photo-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-muted);
}

input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: inherit;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.cancel-button,
.submit-button {
  padding: 0.5em 1em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--step-0);
}

.cancel-button {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

.submit-button {
  background-color: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid var(--color-background);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  color: rgb(239, 68, 68);
  margin-top: 1rem;
  text-align: center;
}
</style>
