<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAutoAnimate } from '@formkit/auto-animate/vue'
import { Plus, X } from 'lucide-vue-next'
import { Act } from '@/models/Act'
import ActBadgeLogo from '@/components/acts/ActBadgeLogo.vue'

type Props = {
  modelValue: string[] | null
  acts: Act[]
  showNames?: boolean
  hideHeader?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  showNames: false,
  hideHeader: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string[] | null]
  'update:showNames': [value: boolean]
}>()

const showActNames = ref(props.showNames !== true)
const showAvailableActs = ref(false)

const selectedActs = computed(() => {
  const chosen = new Set(props.modelValue || [])
  return props.acts.filter(act => chosen.has(act.id))
})

const availableActs = computed(() => {
  const chosen = new Set(props.modelValue || [])
  return props.acts.filter(act => !chosen.has(act.id))
})

const toggleAct = (actId: string) => {
  const newValue = [...(props.modelValue || [])]
  const index = newValue.indexOf(actId)

  if (index === -1) {
    newValue.push(actId)
  } else {
    newValue.splice(index, 1)
  }

  emit('update:modelValue', newValue)
}

watch(() => props.showNames, (newValue) => {
  showActNames.value = newValue
}, { immediate: true })

const toggleAvailableActs = () => {
  showAvailableActs.value = !showAvailableActs.value
}

const [selectedActsParent] = useAutoAnimate()
const [availableActsParent] = useAutoAnimate()
</script>

<template>
  <div class="act-selector">
    <div v-if="!hideHeader" class="act-selector__header">
      <h3 style="outline: 1px solid green;">Acts</h3>
      <BaseToggle v-model="showActNames" size="tiny" class="badges-only-toggle">Act Names</BaseToggle>
    </div>

    <div class="selected-acts">
      <div class="selected-acts-container" ref="selectedActsParent">
        <div v-for="act in selectedActs" :key="act.id" class="act-badge"
          :class="{ 'act-badge-without-name': !showActNames }" @click.prevent="toggleAct(act.id)" :title="act.name">
          <ActBadgeLogo v-if="act.logoUrls?.badge" :actId="act.id" class="act-badge-logo" />
          <div v-else class="act-badge__placeholder">
            {{ act.name[0] }}
          </div>
          <span v-if="showActNames" class="act-badge__name">{{ act.name }}</span>
          <span class="act-badge__indicator act-badge__indicator--remove">
            <X />
          </span>
        </div>
      </div>

      <BaseButton type="button" class="toggle-acts-button" @click.prevent="toggleAvailableActs"
        :class="{ 'toggle-acts-button--active': showAvailableActs }"
        :title="showAvailableActs ? 'Close' : 'Add/Remove Acts'">
        <X v-if="showAvailableActs" />
        <Plus v-else />
      </BaseButton>
    </div>

    <div v-if="showAvailableActs" class="available-acts">
      <div class="available-acts-container" ref="availableActsParent">
        <div v-for="act in availableActs" :key="act.id" class="act-badge"
          :class="{ 'act-badge-without-name': showActNames }" @click.prevent="toggleAct(act.id)" :title="act.name">
          <ActBadgeLogo v-if="act.logoUrls?.badge" :actId="act.id" class="act-badge-logo" />

          <div v-else class="act-badge__placeholder">
            {{ act.name[0] }}
          </div>
          <span v-if="!showActNames" class="act-badge__name">{{ act.name }}</span>
          <span class="act-badge__indicator act-badge__indicator--add">
            <Plus />
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.act-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.selected-acts {
  position: relative;
  min-height: 48px;
  padding: 0.5rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
}

.available-acts {
  overflow: hidden;
  padding: 0.75rem;
  background: var(--color-background-soft);
  border-radius: 0.5rem;
  margin-top: 0.25rem;
}



.act-badge {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  padding-right: 2.5rem;
  border-radius: 1.5rem;
  background: var(--color-background-soft);
  cursor: pointer;
  transition: all 0.2s ease;
}

.act-badge:hover {
  background: var(--color-background-mute);
  transform: translateY(-1px);
}

.act-badge-logo {
  width: 35px;
  height: auto;
  margin-inline-end: .5ch;
}

.act-badge__placeholder {
  background: var(--color-background-mute);
  color: var(--color-text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.act-badge__name {
  font-size: var(--step--1);
  padding-right: 0.75rem;
}

.toggle-acts-button {
  position: absolute;
  bottom: -12px;
  right: -12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  background-color: var(--color-background-mute);
  color: var(--color-text);
  font-size: var(--step-1);
  line-height: 1;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
}

.toggle-acts-button:hover,
.toggle-acts-button--active {
  background-color: var(--color-accent);
  color: var(--color-background);
  transform: scale(1.1);
}

.toggle-acts-button--active {
  font-size: var(--step-2);
}

.act-badge-without-name {
  padding: 0;
  padding-right: 2rem;
}

.act-badge-without-name .act-badge__name {
  display: none;
}

.act-selector__header {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;

  h3 {
    font-size: var(--step--1);
    font-weight: normal;
    font-family: inherit;
  }
}

.badge-toggle {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
  background: var(--color-background-mute);
  cursor: pointer;
  padding: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.badge-toggle .icon {
  width: 16px;
  height: 16px;
  color: var(--color-text-muted);
  transition: all 0.2s ease;
}

.badge-toggle--active {
  background: var(--color-accent);
  border-color: var(--color-accent);
}

.badge-toggle--active .icon {
  color: var(--color-background);
}

.badge-toggle:hover {
  transform: scale(1.1);
}

.selected-acts-container,
.available-acts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  position: relative;
}

/* Transition styles */
.act-enter-active,
.act-leave-active {
  transition: all 0.5s ease;
}

.act-enter-from,
.act-leave-to {
  opacity: 0;
  transform: scale(0.6) translateY(-20px);
  filter: blur(10px);
}

.act-move {
  transition: transform 0.5s ease;
}

/* Slide transition for available acts container */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  max-height: 1000px;
  opacity: 1;
}

.slide-enter-from,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.act-badge__indicator {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--step-0);
  line-height: 0;
  padding-bottom: 1px;
  opacity: 0.15;
  transition: all 0.2s ease;
  background-color: transparent;
  color: var(--color-text);
}

.act-badge__indicator--add {
  background-color: rgb(0, 200, 0);
  color: white;
}

.act-badge__indicator--remove {
  background-color: rgb(200, 0, 0);
  color: white;
  font-size: var(--step-1);
  padding-bottom: 2px;
}

.act-badge:hover .act-badge__indicator--add {
  background-color: rgb(0, 200, 0);
  color: white;
}

.act-badge:hover .act-badge__indicator--remove {
  background-color: rgb(200, 0, 0);
  color: white;
}

.act-badge:hover .act-badge__indicator {
  opacity: 1;
}
</style>
