<script setup lang="ts">
import { Music, Globe } from 'lucide-vue-next'
import { useEntityCard } from '@/composables/useEntityCard'
import { useActs } from '@/composables/acts/useActs'
import EntityActions from '@/components/base/EntityActions.vue'
import EntityImage from '@/components/base/EntityImage.vue'
import { Act } from '@/models/Act'

const props = defineProps<{
  act: Act
}>()

const { deleteAct } = useActs()

const {
  isLoading,
  error,
  handleImageLoad,
  handleImageError,
  viewEntity,
  editEntity,
  deleteEntity
} = useEntityCard({
  entityType: 'act',
  entity: props.act,
  onDelete: () => deleteAct(props.act.id)
})
</script>

<template>
  <BaseCard class="act-card">
    <template #header>
      <div class="act-card__logos">
        <EntityImage v-if="act.logoUrls?.text" :publicId="act.logoUrls.text" :alt="`${act.name} text logo`" height="40"
          class="act-card__logo" @load="handleImageLoad" @error="handleImageError" />
      </div>
    </template>

    <div class="act-card__photo">
      <EntityImage v-if="act.photoUrl" :publicId="act.photoUrl" :alt="`Photo of ${act.name}`" height="200" :fit="true"
        @load="handleImageLoad" @error="handleImageError">
        <div class="act-card__badge-wrapper">
          <EntityImage v-if="act.logoUrls?.badge" :publicId="act.logoUrls.badge" :alt="`${act.name} badge logo`"
            height="50" class="act-card__badge" />
        </div>
      </EntityImage>
    </div>

    <div class="act-card__content">
      <h3 class="act-card__name">{{ act.name }}</h3>
      <p class="act-card__description">{{ act.description }}</p>

      <div class="act-card__meta">
        <div v-if="act.website" class="act-card__website">
          <Globe class="icon" />
          <a :href="act.website" target="_blank" rel="noopener noreferrer">Website</a>
        </div>
        <div v-if="act.artistIds?.length" class="act-card__members">
          <Music class="icon" />
          <span>{{ act.artistIds.length }} {{ act.artistIds.length === 1 ? 'member' : 'members' }}</span>
        </div>
      </div>

      <p v-if="error" class="error-message">{{ error }}</p>
    </div>

    <template #footer>
      <EntityActions :is-loading="isLoading" :can-view="true" :can-edit="true" :can-delete="true" @view="viewEntity"
        @edit="editEntity" @delete="deleteEntity" size="compact" icon />
    </template>
  </BaseCard>
</template>

<style scoped>
.act-card {
  max-width: 500px;
  margin: 0 auto;
  width: 100%;
}

.act-card__logos {
  display: flex;
  justify-content: center;
}

.act-card__logo {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(1px 1px 0 #000A) drop-shadow(-1px -1px 0 #FFFA);
}

.act-card__badge-wrapper {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 50px;
  height: 50px;
  transition: transform 0.2s ease;
  filter: drop-shadow(1px 1px 0 #000);
}

.act-card__badge {
  width: 100%;
  height: 100%;
}

.act-card:hover .act-card__badge-wrapper {
  transform: scale(1.1);
}

.act-card__content {
  display: grid;
  gap: 0.5rem;
}

.act-card__name {
  color: var(--color-heading);
  font-size: var(--step-1);
  margin: 0;
  line-height: 1.2;
}

.act-card__description {
  color: var(--color-text-soft);
  font-size: var(--step--1);
  margin: 0;
  display: -webkit-box;
  overflow: hidden;
}

.act-card__meta {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: var(--step--1);
}

.act-card__website,
.act-card__members {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-muted);
}

.icon {
  width: 1em;
  height: 1em;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: 0;
}
</style>
