<script setup lang="ts">
import { MapPin, Calendar, Clock, Eye } from 'lucide-vue-next'
import EntityActions from '@/components/base/EntityActions.vue'
import EntityImage from '@/components/base/EntityImage.vue'
import { useEntityCard } from '@/composables/useEntityCard'
import { Event } from '@/models/Event'
import type { EventStatus } from '@/types/models'
import { useEvents } from '@/composables/events/useEvents'

type Props = {
  event: Event
}

const props = defineProps<Props>()
const { updateEvent, deleteEvent } = useEvents()

const {
  isLoading,
  error,
  handleImageLoad,
  handleImageError,
  viewEntity,
  editEntity,
} = useEntityCard({
  entityType: 'event',
  entity: props.event
})

const deleteEntity = async () => {
  try {
    isLoading.value = true
    await deleteEvent(props.event.id)
  } catch (err) {
    error.value = new Error('Failed to delete event')
    console.error('Error deleting event:', err)
  } finally {
    isLoading.value = false
  }
}

async function handleStatusChange(newStatus: EventStatus) {
  try {
    isLoading.value = true;
    props.event.status = newStatus;
    await updateEvent(props.event.id, { status: newStatus })
  } catch (err) {
    error.value = new Error('Failed to update event status');
    console.error('Error updating event status:', err);
  } finally {
    isLoading.value = false;
  }
}

function viewOnMap() {
  const coords = props.event.getVenueCoords()
  if (!coords) return

  // Extract latitude and longitude from coords (assuming format is "lat,lng")
  const [latitude, longitude] = coords.split(',').map(coord => coord.trim())

  // Check if we're on a mobile device
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)

  let mapUrl

  if (isMobile) {
    // On iOS, use Apple Maps
    if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
      mapUrl = `maps://maps.apple.com/?q=${latitude},${longitude}`
    }
    // On Android, use geo: protocol which opens the default map app
    else if (/Android/i.test(navigator.userAgent)) {
      mapUrl = `geo:${latitude},${longitude}?q=${latitude},${longitude}`
    }
    // Fallback to Google Maps
    else {
      mapUrl = `https://www.google.com/maps/search/?api=1&query=${coords}`
    }
  } else {
    // On desktop, use Google Maps as fallback
    mapUrl = `https://www.google.com/maps/search/?api=1&query=${coords}`
  }

  window.open(mapUrl, '_blank')
}
</script>

<template>
  <BaseCard class="event-card">
    <template #header>
      <div class="event-card__header">
        <div class="event-card__title">
          <EntityImage v-for="act in event.actDetails" :key="act.id" :public-id="act.logoUrls?.badge!"
            :alt="`${act.displayName} badge`" height="40" class="event-card__act-badge" :title="act.displayName"
            @load="handleImageLoad" @error="handleImageError" />{{ event.shortTitle() }}
        </div>

        <div v-if="event.fee() > 0" class="financials">
          <div>Fee:</div>
          <div>£{{ event.fee() }}</div>

          <div v-if="event.deposit() > 0">Deposit:</div>
          <div v-if="event.deposit() > 0" :class="{ strikethrough: event.isDepositPaid() }">£{{ event.deposit() }}
          </div>

          <div v-if="event.deposit() > 0">Outstanding:</div>
          <div v-if="event.deposit() > 0">£{{ event.outstandingBalance() }}</div>
        </div>
      </div>
    </template>

    <div class="event-card__content">
      <div v-if="!event.isPrivate && event.venueDetails" class="event-card__venue">
        <MapPin class="icon" />
        <div class="event-card__venue-details">
          <span class="event-card__venue-name">{{ event.venueName() }}</span>
          <span v-if="event.venueAddress()" class="event-card__venue-address">
            <a :href="`https://www.google.com/maps/search/?api=1&query=${event.venueName() + ', ' + event.venueAddress()}`"
              target="_blank" rel="noopener noreferrer">
              {{ event.venueAddress() }}
            </a>
          </span>
          <span v-if="event.venueCoords" class="event-card__venue-coords">
            <BaseButton @click="viewOnMap">View on map</BaseButton>
          </span>
          <span v-else>{{ event.venueDetails.address.coords }}</span>
        </div>
      </div>
      <div v-else-if="!event.isPrivate">
        <span>No venue</span>
      </div>
      <div class="event-card__meta">
        <div class="event-card__meta-item">
          <Calendar class="icon" />
          <span>{{ event.formattedDate() }}</span>
        </div>
        <div class="event-card__meta-item">
          <Clock class="icon" />
          <span>{{ event.startTime() }}</span>
        </div>
      </div>
      <div class="external-view-links">
        <a :href="`https://davesroyorbison.com/#/events/${event.id}`" target="_blank" rel="noopener noreferrer"
          class="base-button level-0">
          <Eye class="icon" /> davesroyorbison.com
        </a>
        <a v-if="event.acts.find(act => act === 'human-jukebox')" href="https://daveshumanjukebox.com/events"
          target="_blank" rel="noopener noreferrer" class="base-button level-0">
          <Eye class="icon" /> daveshumanjukebox.com
        </a>
        <a v-if="event.acts.find(act => act === 'orbison-project')"
          :href="`https://theorbisonproject.com/#/events/${event.id}`" target="_blank" rel="noopener noreferrer"
          class="base-button level-0">
          <Eye class="icon" /> theorbisonproject.com
        </a>

      </div>
      <p v-if="error" class="error-message">{{ error.message }}</p>
    </div>

    <template #footer>

      <div class="event-card__footer">
        <small class="event-id">
          <span v-if="event.isPrivate" class="is-private">Private</span>
          ID: {{ event.id }}
        </small>
        <EntityActions :is-loading="isLoading" :show-status-controls="true" :status="event.status"
          :is-past="event.isPast()" @view="viewEntity" @edit="editEntity" @delete="deleteEntity"
          @status-change="handleStatusChange" icon />
      </div>
    </template>
  </BaseCard>
</template>

<style scoped>
.event-card {
  container-type: inline-size;
  background-color: var(--color-bg-1);
}

.event-id {
  font-size: var(--step--2);
  color: var(--color-text-muted);
}

.is-private {
  padding: var(--space-3xs) var(--space-2xs);
  background-color: var(--color-danger-dark);
  border-radius: var(--space-3xs);
  color: var(--color-text);
  margin-inline-end: var(--space-3xs);
}

.event-card__header {
  gap: 0.5rem;
  display: flex;
  flex-wrap: wrap;
}

.event-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: end;
}

.event-card__layout {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-m);
  align-items: center;
}

.event-card__main {
  display: grid;
  gap: var(--space-m);
}

.event-card__actions {
  display: flex;
  align-items: center;
  padding-left: var(--space-m);
  border-left: 1px solid var(--color-border);
}

.event-card__title {
  margin: 0;
  font-size: var(--step-1);
  color: var(--color-heading);
  line-height: 1.2;
  text-align: left;
  display: flex;
  gap: var(--space-3xs);
  align-items: center;
  flex: 1;
}

.event-card__meta {
  display: flex;
  gap: 1rem;
  color: var(--color-text-soft);
  font-size: var(--step-0);
}

.event-card__meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.event-card__content {
  display: grid;
  gap: 1rem;
}

.event-card__venue {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.event-card__venue-details {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  gap: var(--space-3xs) var(--space-xs);
}

.event-card__venue-name {
  color: var(--color-text);
  font-weight: 500;
}

.event-card__venue-address {
  color: var(--color-text-soft);
  font-size: var(--step--1);
}

.event-card__acts {
  display: grid;
  gap: 0.5rem;
}

.event-card__acts-header {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.event-card__acts-list {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.event-card__act-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.event-card__description {
  margin: 0;
  color: var(--color-text-soft);
  font-size: var(--step--1);
}

.event-card__description :deep(a) {
  color: var(--color-primary);
  text-decoration: none;
}

.event-card__description :deep(a:hover) {
  text-decoration: underline;
}

.event-card__description :deep(p) {
  margin: 0.5rem 0;
}

.event-card__description :deep(p:first-child) {
  margin-top: 0;
}

.event-card__description :deep(p:last-child) {
  margin-bottom: 0;
}

.external-view-links {
  display: flex;
  gap: var(--space-2xs);
  background-color: var(--color-bg-2);
  width: fit-content;
  padding: var(--space-3xs) var(--space-3xs);
  border-radius: var(--radius-m);

  a {
    display: flex;
    align-items: center;
    gap: var(--space-2xs);
    text-decoration: none;
    color: var(--color-text);
    font-size: var(--step--1);
    background-color: var(--color-bg-3);
    padding: var(--space-3xs) var(--space-s);
    border-radius: var(--radius-s);

    &:hover {
      background-color: var(--color-bg-4);
      translate: 0 -2px;
      box-shadow: var(--shadow-m);
    }

    .icon {
      color: var(--color-primary-light);
    }
  }
}

.financials {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-3xs) 1ch;
  justify-items: end;
  font-size: var(--step--1);
  background-color: var(--color-background-mute);
  border-radius: var(--space-2xs);
  padding: var(--space-2xs) var(--space-s);
  color: var(--color-text-soft);
  font-variant-numeric: tabular-nums;
}

.icon {
  width: 1em;
  height: 1em;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: 0;
}

@container card (max-width: 640px) {
  .event-card__layout {
    grid-template-columns: 1fr;
  }

  .event-card__actions {
    padding-left: 0;
    padding-top: var(--space-m);
    border-left: none;
    border-top: 1px solid var(--color-border);
  }
}

.strikethrough {
  text-decoration: line-through;
  opacity: 0.7;
}

.event-card__fee-badges {
  display: flex;
  align-items: center;
  gap: var(--space-s);
}

.event-card__fee-badge {
  text-align: right;
  line-height: 1.2;
}
</style>
