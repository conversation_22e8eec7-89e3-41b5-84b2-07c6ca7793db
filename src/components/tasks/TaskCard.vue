<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useTimeAgo } from '@vueuse/core'
import { AlertTriangle, Check, Clock, PencilLine, Trash2 } from 'lucide-vue-next'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { useUsers } from '@/composables/user/useUsers'
import type { Task } from '@/models/Task'

const props = defineProps<{
  task: Task
}>()

const emit = defineEmits<{
  (e: 'toggle-completion', id: string, isCompleted: boolean): void
  (e: 'toggle-urgency', id: string, isUrgent: boolean): void
  (e: 'delete', id: string): void
  (e: 'edit', task: Task): void
}>()

const { currentUser } = useFirebase()
const { findUserByEmail, subscribeToUsers } = useUsers()

onMounted(() => {
  subscribeToUsers()
})

const isAuthor = computed(() => props.task.authorEmail === currentUser.value?.email)

const assignedUser = computed(() => {
  if (!props.task.assignedTo) return null
  return findUserByEmail(props.task.assignedTo)
})

const completedByUser = computed(() => {
  if (!props.task.completedBy) return null
  return findUserByEmail(props.task.completedBy)
})

const authoredByUser = computed(() => {
  if (!props.task.authorEmail) return null
  return findUserByEmail(props.task.authorEmail)
})

const isOverdue = computed(() => {
  if (!props.task.dueDate || props.task.isCompleted) return false
  return props.task.dueDate < new Date()
})

const isDueSoon = computed(() => {
  if (!props.task.dueDate || props.task.isCompleted || isOverdue.value) return false
  const threeDaysFromNow = new Date()
  threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3)
  return props.task.dueDate <= threeDaysFromNow
})

const dueDateFormatted = computed(() => {
  if (!props.task.dueDate) return ''
  return new Intl.DateTimeFormat('en-GB', {
    dateStyle: 'medium'
  }).format(props.task.dueDate)
})

const completedDateFormatted = computed(() => {
  if (!props.task.completedAt) return ''
  return new Intl.DateTimeFormat('en-GB', {
    dateStyle: 'medium'
  }).format(props.task.completedAt)
})

const completedTimeAgo = computed(() => {
  if (!props.task.completedAt) return ''
  return useTimeAgo(props.task.completedAt).value
})

const createdDateFormatted = computed(() => {
  return new Intl.DateTimeFormat('en-GB', {
    dateStyle: 'medium'
  }).format(props.task.createdAt)
})

const createdTimeAgo = computed(() => useTimeAgo(props.task.createdAt).value)

const timeAgo = computed(() => useTimeAgo(props.task.createdAt).value)

const confirmDelete = () => {
  if (window.confirm('Are you sure you want to delete this task?')) {
    emit('delete', props.task.id)
  }
}
</script>

<template>
  <BaseCard class="task-card" :class="{
    'task-card--completed': task.isCompleted,
    'task-card--urgent': task.isUrgent && !task.isCompleted,
    'task-card--overdue': isOverdue,
    'task-card--due-soon': isDueSoon
  }">
    <div class="task-card__header">
      <details class="task-card__title">
        <summary>
          <h3 class="task-card__title-text">{{ task.title }}</h3>
          <div class="task-card__badges">
            <BaseButton v-if="isAuthor" variant="outline" purpose="danger" @click="confirmDelete" title="Delete Task">
              <Trash2 class="icon" />
            </BaseButton>
            <BaseButton v-if="isAuthor && !task.isCompleted" variant="outline" purpose="secondary"
              @click="emit('edit', task)" title="Edit Task">
              <PencilLine class="icon" />
            </BaseButton>
            <BaseButton v-if="isAuthor && !task.isUrgent && !task.isCompleted" variant="outline" purpose="warning"
              @click="emit('toggle-urgency', task.id, true)" title="Mark as Urgent">
              <AlertTriangle class="icon" />
            </BaseButton>
            <BaseButton v-else-if="isAuthor && !task.isCompleted" purpose="warning"
              @click="emit('toggle-urgency', task.id, false)" title="Remove Urgent">
              <AlertTriangle class="icon" />
            </BaseButton>
            <BaseButton v-if="!task.isCompleted" variant="outline" purpose="success"
              @click="emit('toggle-completion', task.id, true)" title="Mark as Complete">
              <Check class="icon" />
            </BaseButton>
            <BaseButton v-if="task.isCompleted" purpose="success" @click="emit('toggle-completion', task.id, false)"
              title="Mark as Incomplete">
              <Check />
            </BaseButton>
          </div>
        </summary>
        <div v-if="task.details" class="task-card__details" v-html="task.details"></div>
      </details>

      <div v-if="!task.isCompleted" class="task-card__meta">
        <div v-if="task.dueDate" class="task-card__due-date" :class="{
          'task-card__due-date--overdue': isOverdue,
          'task-card__due-date--due-soon': isDueSoon
        }">
          <Clock :size="18" /> Due {{ dueDateFormatted }} {{ isDueSoon ? '(soon)' : '' }} {{ isOverdue ? '(overdue)' :
            '' }}
        </div>
        <div v-if="task.assignedTo" class="task-card__assigned">
          Assigned to: {{ assignedUser?.displayName || task.assignedTo }}
        </div>
      </div>

      <div v-if="task.isCompleted && task.completedAt && task.completedBy" class="task-card__completed">
        Completed by {{ completedByUser?.displayName || task.completedBy }} on {{ completedDateFormatted }} ({{
          completedTimeAgo }})
      </div>

      <div v-if="!task.isCompleted" class="task-card__info">
        <small>Created by {{ authoredByUser?.displayName || task.author }} on {{ createdDateFormatted }} ({{
          createdTimeAgo }})</small>
      </div>
    </div>
  </BaseCard>
</template>

<style scoped>
.task-card {
  display: flex;
  flex-direction: column;
  gap: var(--space-gap-s);
  border: 1px solid var(--color-border);
}

.task-card__header {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
  gap: var(--space-gap-2xs);
}

.task-card__title {
  width: 100%;
}

.task-card__title h3 {
  font-size: var(--step-0);
  font-weight: 500;
  margin: 0;
}

.task-card__title summary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-s);
  flex-wrap: wrap;
  cursor: pointer;
  list-style: none;
}

.task-card__title summary::-webkit-details-marker {
  display: none;
}

.task-card__title-text {
  opacity: 0.8;
}

.task-card__badges {
  display: flex;
  gap: var(--space-xs);
}

.task-card__meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  font-size: var(--step--1);
  color: var(--color-text-muted);
}

.task-card__due-date {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-text-muted);
}

.task-card__due-date--overdue {
  color: var(--color-danger);
  font-weight: 500;
}

.task-card__due-date--due-soon {
  color: var(--color-warning);
  font-weight: 500;
}

.task-card__assigned {
  color: var(--color-text-muted);
}

.task-card__details {
  color: var(--color-text-muted);
  padding: var(--space-m);
  margin-top: var(--space-m);
  background-color: var(--color-background-soft);
  border-radius: var(--radius);
}

.task-card__footer {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.task-card__actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.task-card__actions .icon {
  width: var(--step-0);
  height: var(--step-0);
}

.task-card__info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.task-card--completed {
  opacity: 0.7;

  .task-card__title-text {
    color: var(--color-text-muted);
  }
}

.task-card--urgent {
  border-color: var(--color-warning);
}

.task-card__created {
  color: var(--color-text-muted);
}

.task-card--overdue {
  border-color: var(--color-danger);
}

.task-card--due-soon {
  border-color: var(--color-warning);
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-s);
  border-radius: var(--radius);
  font-size: var(--step--1);
  font-weight: 500;
}

.badge--urgent {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.badge--completed {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
}

.icon {
  width: var(--step-0);
  height: var(--step-0);
}

.task-card__completed {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  margin-top: var(--space-xs);
}
</style>
