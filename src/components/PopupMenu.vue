<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import Icon from '@/Icon.vue'

type MenuItem = {
  id: string
  label: string
  icon?: string
  action?: () => void
  items?: MenuItem[] // For future sub-menu support
  disabled?: boolean
}

type Props = {
  items: MenuItem[]
  position?: 'top' | 'bottom' | 'left' | 'right'
  anchor?: HTMLElement
}

const props = withDefaults(defineProps<Props>(), {
  position: 'bottom',
  anchor: undefined
})

const emit = defineEmits<{
  (e: 'select', item: MenuItem): void
  (e: 'close'): void
}>()

const menuRef = ref<HTMLElement | null>(null)
const isVisible = ref(true)

// Close menu when clicking outside
function handleClickOutside(event: MouseEvent) {
  if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
    isVisible.value = false
    emit('close')
  }
}

// Position the menu relative to the anchor element
function updatePosition() {
  if (!menuRef.value || !props.anchor) return

  const anchorRect = props.anchor.getBoundingClientRect()
  const menuRect = menuRef.value.getBoundingClientRect()

  let top = 0
  let left = 0

  switch (props.position) {
    case 'bottom':
      top = anchorRect.bottom + 8
      left = anchorRect.left - (menuRect.width / 2) + (anchorRect.width / 2)
      break
    case 'top':
      top = anchorRect.top - menuRect.height - 8
      left = anchorRect.left - (menuRect.width / 2) + (anchorRect.width / 2)
      break
    case 'left':
      top = anchorRect.top - (menuRect.height / 2) + (anchorRect.height / 2)
      left = anchorRect.left - menuRect.width - 8
      break
    case 'right':
      top = anchorRect.top - (menuRect.height / 2) + (anchorRect.height / 2)
      left = anchorRect.right + 8
      break
  }

  // Keep menu within viewport
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (left < 8) left = 8
  if (left + menuRect.width > viewport.width - 8) {
    left = viewport.width - menuRect.width - 8
  }
  if (top < 8) top = 8
  if (top + menuRect.height > viewport.height - 8) {
    top = viewport.height - menuRect.height - 8
  }

  menuRef.value.style.top = `${top}px`
  menuRef.value.style.left = `${left}px`
}

function handleSelect(item: MenuItem) {
  if (item.disabled) return
  emit('select', item)
  if (item.action) item.action()
  isVisible.value = false
  emit('close')
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  if (props.anchor) updatePosition()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="menuRef" class="popup-menu" :class="{ 'popup-menu--visible': isVisible }">
    <ul class="popup-menu__list">
      <li v-for="item in props.items" :key="item.id" class="popup-menu__item"
        :class="{ 'popup-menu__item--disabled': item.disabled }" @click="handleSelect(item)">
        <Icon v-if="item.icon" :name="item.icon" size="1.2em" class="popup-menu__icon" />
        <span class="popup-menu__label">{{ item.label }}</span>
        <Icon v-if="item.items?.length" name="chevronRight" size="1em" class="popup-menu__arrow" />
      </li>
    </ul>
  </div>
</template>

<style scoped>
.popup-menu {
  position: fixed;
  z-index: 1000;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  opacity: 0;
  transform: scale(0.95);
  transform-origin: top center;
  transition: all 0.1s ease-out;
  pointer-events: none;
}

.popup-menu--visible {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.popup-menu__list {
  margin: 0;
  padding: 4px;
  list-style: none;
}

.popup-menu__item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: var(--color-text);
  cursor: pointer;
  border-radius: var(--border-radius);
  gap: 8px;
  user-select: none;
}

.popup-menu__item:hover:not(.popup-menu__item--disabled) {
  background: var(--color-background-soft);
}

.popup-menu__item--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.popup-menu__icon {
  flex-shrink: 0;
  color: var(--color-text-light);
}

.popup-menu__label {
  flex-grow: 1;
  font-size: var(--step--1);
}

.popup-menu__arrow {
  flex-shrink: 0;
  color: var(--color-text-light);
  margin-left: auto;
}
</style>
