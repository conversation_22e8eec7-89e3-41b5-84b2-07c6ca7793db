<script setup lang="ts">
import DashboardCard from '@/components/DashboardCard.vue'
import SchemaAnalyzer from '@/components/SchemaAnalyzer.vue'
</script>

<template>
  <DashboardCard class="dashboard-schema-card" title="Database Schema" icon="database">
    <div class="dashboard-schema-card__content">
      <SchemaAnalyzer />
    </div>
  </DashboardCard>
</template>

<style scoped>
.dashboard-schema-card {
  container-type: inline-size;
}

.dashboard-schema-card__header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dashboard-schema-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--color-brand-soft);
  color: var(--color-brand);
  border-radius: 0.75rem;
}

.dashboard-schema-card__title {
  text-decoration: none;
}

.dashboard-schema-card__title h2 {
  margin: 0;
  font-size: var(--step-1);
  color: var(--color-brand);
}

.dashboard-schema-card__content {
  display: grid;
  gap: 1rem;
}
</style>
