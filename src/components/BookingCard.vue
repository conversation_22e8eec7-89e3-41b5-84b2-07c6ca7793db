<script setup lang="ts">
import { computed } from 'vue'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { useDateTime } from '@/composables/useDateTime'
import EntityActions from '@/components/base/EntityActions.vue'
import { useEntityCard } from '@/composables/useEntityCard'
import { Act } from '@/models/Act'
import { Venue } from '@/models/Venue'
import { Event } from '@/models/Event'

type Props = {
  event: Event
}

const props = defineProps<Props>()

const { isAdmin } = useFirebase()
const { formatEventDateTime } = useDateTime()

const mainAct = computed<Act | undefined>(() => {
  return props.event.actDetails?.[0]
})

const venue = computed<Venue | null>(() => {
  return props.event.venueDetails
})

const {
  isLoading,
  error,
  viewEntity
} = useEntityCard({
  entityType: 'events',
  entity: props.event
})
</script>

<template>
  <BaseCard class="booking-card" variant="default">
    <template #header>
      <div class="booking-card__header">
        <h3 class="booking-card__title">{{ event.title || mainAct?.name }}</h3>
        <p class="booking-card__datetime">{{ formatEventDateTime(event.when) }}</p>
      </div>
    </template>

    <div class="booking-card__content">
      <p v-if="venue?.name" class="booking-card__venue">
        {{ venue.name }}
      </p>
      <p v-if="event.description" class="booking-card__description">
        {{ event.description }}
      </p>
      <p v-if="error" class="error-message">{{ error }}</p>
    </div>

    <template v-if="isAdmin" #footer>
      <EntityActions :is-loading="isLoading" :can-edit="false" :can-delete="false" @view="viewEntity" />
    </template>
  </BaseCard>
</template>

<style scoped>
.booking-card__header {
  display: grid;
  gap: 0.5rem;
}

.booking-card__title {
  margin: 0;
  font-size: var(--step-1);
  color: var(--color-accent);
}

.booking-card__datetime {
  margin: 0;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.booking-card__content {
  display: grid;
  gap: 0.5rem;
}

.booking-card__venue {
  margin: 0;
  font-weight: 500;
}

.booking-card__description {
  margin: 0;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: 0;
}
</style>
