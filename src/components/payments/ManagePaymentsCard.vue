<script setup lang="ts">
import { ref } from 'vue'
import { Check, Edit2 } from 'lucide-vue-next'
import { Event } from '@/models/Event'
import { Timestamp } from 'firebase/firestore'

const props = defineProps<{
  event: Event
}>()

const emit = defineEmits<{
  (e: 'update', eventId: string, updates: any): void
}>()

// Payment editing state
const editingField = ref<'fee' | 'deposit' | null>(null)
const paymentAmount = ref<number | null>(null)
const paymentDate = ref<string>(new Date().toISOString().split('T')[0])
const isPaid = ref<boolean>(false)

// Format currency
function formatCurrency(amount: number | null): string {
  if (amount === null || amount === 0) return '---'
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date
function formatDate(date: Date | null): string {
  if (!date) return '—'
  return date.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Start editing payment
function startEditing(field: 'fee' | 'deposit') {
  editingField.value = field

  if (field === 'fee') {
    paymentAmount.value = props.event.fee()
    isPaid.value = props.event.isFeePaid()
    const paymentDateValue = props.event.feePaymentDate()
    paymentDate.value = paymentDateValue
      ? paymentDateValue.toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0]
  } else {
    paymentAmount.value = props.event.deposit()
    isPaid.value = props.event.isDepositPaid()
    const paymentDateValue = props.event.depositPaymentDate()
    paymentDate.value = paymentDateValue
      ? paymentDateValue.toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0]
  }
}

// Cancel editing
function cancelEditing() {
  editingField.value = null
  paymentAmount.value = null
  isPaid.value = false
}

// Save payment changes
async function savePayment() {
  if (!editingField.value) return

  try {
    // Set the save callback before updating
    props.event.setSaveCallback(async (id, updates) => {
      emit('update', id, updates)
    })

    // Update the event with payment details
    await props.event.setPaymentDetails(editingField.value, {
      amount: paymentAmount.value,
      paid: isPaid.value,
      date: isPaid.value ? Timestamp.fromDate(new Date(paymentDate.value)) : null
    })

    // Reset editing state
    cancelEditing()
  } catch (error) {
    console.error('Error saving payment:', error)
  }
}
</script>

<template>
  <BaseCard class="payment-event-card">
    <div class="payment-event-header">
      <div class="payment-event-date">
        {{ formatDate(event.when.toDate()) }}
      </div>
      <h3 class="payment-event-venue">{{ event.venueName() }}</h3>
      <small class="payment-event-acts">
        {{ event.actDisplayNames() }}
      </small>
    </div>

    <div class="payment-details">
      <div class="payment-section" :class="{ 'is-editing': editingField === 'fee' }">
        <div class="payment-label" v-if="editingField !== 'fee'">
          Fee:
        </div>

        <div v-if="editingField === 'fee'" class="payment-edit-form">
          <div class="payment-form-title">
            Fee
          </div>
          <div class="payment-form-row">
            <label>Amount:</label>
            <input type="number" v-model="paymentAmount" min="0" step="1" class="payment-input" />
          </div>

          <div class="payment-form-row">
            <label>Paid:</label>
            <BaseToggle v-model="isPaid" size="compact" />
          </div>

          <div v-if="isPaid" class="payment-form-row">
            <label>Date:</label>
            <input type="date" v-model="paymentDate" class="payment-input" />
          </div>

          <div class="payment-form-actions">
            <BaseButton @click="cancelEditing" purpose="danger" size="small">Cancel</BaseButton>
            <BaseButton @click="savePayment" purpose="success" size="small">
              <Check class="icon" /> Save
            </BaseButton>
          </div>
        </div>

        <div v-else class="payment-value-section">
          <div class="payment-value" :class="{ 'not-set': !event.fee() }">{{ formatCurrency(event.fee()) }}</div>
          <div class="payment-status" :class="{ 'is-paid': event.isFeePaid() }">
            {{ event.isFeePaid() ? 'Paid' : 'Unpaid' }}
            <div v-if="event.isFeePaid()" class="payment-date">
              {{ formatDate(event.feePaymentDate()) }}
            </div>
          </div>
          <BaseButton @click="startEditing('fee')" purpose="primary" size="compact">
            <Edit2 class="icon" />
          </BaseButton>
        </div>
      </div>

      <!-- Deposit section -->
      <div class="payment-section" :class="{ 'is-editing': editingField === 'deposit' }">
        <div class="payment-label" v-if="editingField !== 'deposit'">
          Deposit:
        </div>

        <div v-if="editingField === 'deposit'" class="payment-edit-form">
          <div class="payment-form-title">
            Deposit
          </div>

          <div class="payment-form-row">
            <label>Amount:</label>
            <input type="number" v-model="paymentAmount" min="0" step="1" class="payment-input" />
          </div>

          <div class="payment-form-row">
            <label>Paid:</label>
            <BaseToggle v-model="isPaid" size="compact" />
          </div>

          <div v-if="isPaid" class="payment-form-row">
            <label>Date:</label>
            <input type="date" v-model="paymentDate" class="payment-input" />
          </div>

          <div class="payment-form-actions">
            <BaseButton @click="cancelEditing" purpose="danger" size="small">Cancel</BaseButton>
            <BaseButton @click="savePayment" purpose="success" size="small">
              <Check class="icon" /> Save
            </BaseButton>
          </div>
        </div>

        <div v-else class="payment-value-section">
          <div class="payment-value" :class="{ 'not-set': !event.deposit() }">{{ formatCurrency(event.deposit()) }}
          </div>
          <div class="payment-status" :class="{ 'is-paid': event.isDepositPaid() }">
            {{ event.isDepositPaid() ? 'Paid' : 'Unpaid' }}
            <div v-if="event.isDepositPaid()" class="payment-date">
              {{ formatDate(event.depositPaymentDate()) }}
            </div>
          </div>
          <BaseButton @click="startEditing('deposit')" purpose="primary" size="compact">
            <Edit2 class="icon" />
          </BaseButton>
        </div>
      </div>

      <div v-if="event.fee() > 0 && event.isPast()" class="payment-balance">
        <div>Outstanding: {{ formatCurrency(event.outstandingBalance()) }}</div>
      </div>
    </div>

  </BaseCard>
</template>

<style scoped>
.payment-event-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-s);
}

.payment-event-venue {
  font-size: var(--step-1);
}

.payment-details {
  display: flex;
  flex-wrap: wrap;
  /* justify-content: space-evenly; */
  gap: var(--space-m) var(--space-xl);
}

.payment-section {
  display: flex;
  align-items: center;
  gap: var(--space-m);
}

.payment-value-section {
  display: flex;
  align-items: center;
  gap: var(--space-s);
}

.payment-value {
  font-weight: bold;
  color: var(--color-text);

  &.not-set {
    color: var(--color-warning-muted);
  }
}

.payment-status {
  color: var(--color-text-muted);
}

.payment-status.is-paid {
  color: var(--color-success);
  display: grid;
  font-weight: bold;
  background-color: var(--color-bg-1);
  text-align: center;
  gap: var(--space-3xs);
  line-height: 1;
  padding: var(--space-3xs) var(--space-xs);
  border-radius: var(--radius-m);
}

.payment-date {
  font-size: var(--step--1);
  color: var(--color-text-soft);
}

.payment-edit-form {
  background-color: var(--color-bg-1);
  border-radius: var(--radius-m);
  border: 1px solid var(--color-border);
  padding: var(--space-xs);
  width: 100%;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--space-3xs);
}

.payment-form-title {
  font-size: var(--step-0);
  font-weight: bold;
  text-align: center;
  grid-column: 1 / -1;

  &::before {
    content: 'Edit ';
    font-weight: bold;
  }
}

.payment-form-row {
  display: flex;
  align-items: center;
  gap: var(--space-s);
  grid-column: 1 / -1;
}

.payment-form-row label {
  width: 100px;
  text-align: right;
}

.payment-input {
  flex: 1;
}

.payment-form-actions {
  grid-column: 1 / -1;
  display: flex;
  gap: var(--space-s);
  justify-content: flex-end;
  margin-top: var(--space-3xs);
}

.payment-balance {
  flex-grow: 1;
  text-align: right;
  color: var(--color-warning);
}
</style>
