<script setup lang="ts">
import { computed } from 'vue'
import EntityImage from '@/components/base/EntityImage.vue'
import { Artist } from '@/models/Artist';

const props = withDefaults(defineProps<{
  artist: Artist
  title?: string
  size?: 'x-small' | 'small' | 'medium' | 'large' | 'x-large'
}>(), {
  title: '',
  size: 'medium'
})

const imageSize = computed(() => {
  switch (props.size) {
    case 'x-small':
      return '1em';
    case 'small':
      return '1.5em';
    case 'medium':
      return '2em';
    case 'large':
      return '3em';
    case 'x-large':
      return '8em';
    default:
      return '2em';
  }
})
</script>

<template>
  <EntityImage v-if="artist.photoId" :publicId="artist.photoId" :alt="artist.stageName || artist.displayName"
    :title="title" :width="imageSize" :height="imageSize" :fit="true" rounded class="artist-avatar" />
  <div v-else class="artist-avatar artist-avatar--fallback" :style="{ width: imageSize, height: imageSize }">
    {{ (artist?.initials) || '?' }}
  </div>
</template>

<style scoped>
.artist-avatar {
  width: v-bind(imageSize);
  height: v-bind(imageSize);
  aspect-ratio: 1;
  border-radius: 50%;
}

.artist-avatar--fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-mute);
  color: var(--color-text-muted);
  font-weight: 500;
  font-size: calc(v-bind(imageSize) * 0.4);
}
</style>
