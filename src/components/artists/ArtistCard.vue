<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import EntityActions from '@/components/base/EntityActions.vue'
import EntityImage from '@/components/base/EntityImage.vue'
import { useEntityCard } from '@/composables/useEntityCard'
import { useActs } from '@/composables/acts/useActs'
import { Artist } from '@/models/Artist'
import BaseBadge from '@/components/base/BaseBadge.vue'

type Props = {
  artist: Artist
  grid?: boolean
}

const props = defineProps<Props>()

const {
  isLoading,
  error,
  viewEntity,
  editEntity,
  deleteEntity
} = useEntityCard({
  entityType: 'artist',
  entity: props.artist
})

const { acts, subscribeToActs, cleanup } = useActs()

// Get acts that this artist is associated with
const artistActs = computed(() => {
  if (!props.artist.id) return []
  return acts.value.filter(act => act.hasArtist(props.artist.id))
})

onMounted(() => {
  subscribeToActs()
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <BaseCard class="artist-card" :class="{ 'artist-card--grid': grid }">
    <template #header>
      <h3 class="artist-card__name">
        <span>{{ artist.displayName }}</span>
        <span class="artist-card__full-name">{{ artist.fullName !== artist.displayName ? artist.fullName : '&nbsp;'
        }}</span>
      </h3>
      <div class="artist-card__photo">
        <!-- TODO: Bring consistency ro all artist avatar components -->
        <EntityImage v-if="artist.photoId" :publicId="artist.photoId"
          :alt="`Photo of ${artist.firstName} ${artist.lastName}`" height="200px" width="200px" :fit="true" rounded>
        </EntityImage>
      </div>
    </template>

    <div class="artist-card__content">
      <hr>
      <div class="artist-card__info">
        <h3 class="artist-card__instruments-label">Instruments</h3>
        <div v-if="artist.instruments" class="artist-card__instruments">
          <BaseBadge v-for="instrument in artist.instruments" size="xs" :key="instrument" purpose="secondary"
            variant="ghost">{{
              instrument }}
          </BaseBadge>
        </div>
      </div>

      <div class="artist-card__info">
        <h3 class="artist-card__acts-label">Acts</h3>
        <div v-if="artistActs.length" class="artist-card__acts">
          <BaseBadge v-for="act in artistActs" size="xs" :key="act.id" purpose="primary" variant="ghost">{{
            act.getDisplayName() }}
          </BaseBadge>
        </div>
        <p v-else class="artist-card__no-acts">No acts assigned</p>
      </div>
      <hr>

      <p v-if="error" class="error-message">{{ error }}</p>
    </div>

    <template #footer>
      <EntityActions :is-loading="isLoading" @view="viewEntity" @edit="editEntity" @delete="deleteEntity" icon />
    </template>
  </BaseCard>
</template>

<style scoped>
.artist-card {
  container-type: inline-size;
}

.artist-card__photo {
  margin: 0 auto;
}

.artist-card__loading,
.artist-card__placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-mute);
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.artist-card__placeholder-icon {
  width: 3rem;
  height: 3rem;
  opacity: 0.5;
}

.artist-card__content {
  display: grid;
  gap: 0.5rem;
}

.artist-card__info {
  display: grid;
  gap: 0.33rem;
}

.artist-card__name {
  color: var(--color-heading);
  font-size: var(--step-1);
  margin: 0;
  line-height: 1.2;

  .artist-card__full-name {
    display: block;
    font-size: var(--step--1);
    color: var(--color-text-muted);
  }
}

.artist-card__instruments {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.artist-card__instruments-label {
  font-size: var(--step-0);
  font-weight: normal;
  color: var(--color-text-soft);
}

.artist-card__acts {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.artist-card__acts-label {
  font-size: var(--step-0);
  font-weight: normal;
  color: var(--color-text-soft);
}

.artist-card__no-acts {
  color: var(--color-text-muted);
  font-size: var(--step--1);
  font-style: italic;
  margin: 0;
}

.icon {
  width: 1em;
  height: 1em;
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: 0;
}

/* Grid Layout */
.artist-card--grid {
  height: 100%;
}

@container card (max-width: 300px) {
  .artists-list .artist-card__content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 0.5rem;
  }

  .artists-list .artist-card__info {
    justify-items: center;
  }

  .artists-list .artist-card__instruments {
    justify-content: center;
  }

  .artists-list .artist-card__acts {
    justify-content: center;
  }
}
</style>
