import type { Meta, StoryObj } from '@storybook/vue3-vite'
import { ref } from 'vue'
import { X, Star, Heart, Bell, User, Settings, Mail } from 'lucide-vue-next'
import BaseBadge from '@/components/base/BaseBadge.vue'

const meta: Meta<typeof BaseBadge> = {
  title: 'Base/BaseBadge',
  component: BaseBadge,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# BaseBadge

A versatile badge component for displaying short text, numbers, status indicators, or icons with consistent styling.

## Features

- **Multiple Sizes**: xs, sm, md, lg variants
- **Shape Options**: rounded, pill, square, and octostar variants
- **Style Variants**: solid, outline, ghost, and dark styles
- **Purpose Colors**: default, primary, secondary, accent, success, danger, warning, and info
- **Closeable**: Optional close button functionality
- **Compact Mode**: Automatically adjusts for single characters or icons
- **Accessibility**: Full keyboard navigation support and ARIA labels

## Usage

\`\`\`vue
<BaseBadge variant="solid" purpose="primary" size="md">
  Badge Text
</BaseBadge>

<BaseBadge :closeable="true" @close="handleClose">
  Closeable Badge
</BaseBadge>

<BaseBadge shape="octostar" purpose="warning">
  <Star :size="16" />
</BaseBadge>
\`\`\`

## Props

- **variant**: 'solid' | 'outline' | 'ghost' | 'dark' (default: 'solid')
- **purpose**: 'default' | 'primary' | 'secondary' | 'accent' | 'success' | 'danger' | 'warning' | 'info' (default: 'primary')
- **size**: 'xs' | 'sm' | 'md' | 'lg' (default: 'sm')
- **shape**: 'rounded' | 'pill' | 'square' | 'octostar' (default: 'rounded')
- **closeable**: boolean (default: false)
- **outlined**: boolean (default: false)

## Events

- **close**: Emitted when the close button is clicked
        `,
      },
      canvas: {
        sourceState: 'shown',
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['solid', 'outline', 'ghost', 'dark'],
      description: 'Visual style variant of the badge',
    },
    purpose: {
      control: 'select',
      options: [
        'default',
        'primary',
        'secondary',
        'accent',
        'success',
        'danger',
        'warning',
        'info',
      ],
      description: 'Semantic purpose determining the color scheme',
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg'],
      description: 'Size of the badge',
    },
    shape: {
      control: 'select',
      options: ['rounded', 'pill', 'square', 'octostar'],
      description: 'Shape variant of the badge',
    },
    closeable: {
      control: 'boolean',
      description: 'Whether the badge can be closed',
    },
    outlined: {
      control: 'boolean',
      description: 'Whether the badge has transparent background with border',
    },
    default: {
      control: 'text',
      description: 'Default slot content',
    },
    onClose: {
      action: 'close',
      description: 'Emitted when the close button is clicked',
    },
  },
  args: {
    variant: 'solid',
    purpose: 'primary',
    size: 'sm',
    shape: 'rounded',
    closeable: false,
    outlined: false,
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'The basic badge with default styling and text content.',
      },
    },
  },
  args: {
    default: 'Badge',
  },
  render: args => ({
    components: { BaseBadge },
    setup() {
      return { args }
    },
    template: `
      <BaseBadge v-bind="args">
        {{ args.default }}
      </BaseBadge>
    `,
  }),
}

export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different size variants of the badge component.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge size="xs">XS Badge</BaseBadge>
        <BaseBadge size="sm">SM Badge</BaseBadge>
        <BaseBadge size="md">MD Badge</BaseBadge>
        <BaseBadge size="lg">LG Badge</BaseBadge>
      </div>
    `,
  }),
}

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different visual style variants of the badge.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge variant="solid" purpose="primary">Solid</BaseBadge>
        <BaseBadge variant="outline" purpose="primary">Outline</BaseBadge>
        <BaseBadge variant="ghost" purpose="primary">Ghost</BaseBadge>
        <BaseBadge variant="dark" purpose="primary">Dark</BaseBadge>
      </div>
    `,
  }),
}

export const Purposes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different semantic purposes showing various color schemes.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge purpose="default">Default</BaseBadge>
        <BaseBadge purpose="primary">Primary</BaseBadge>
        <BaseBadge purpose="secondary">Secondary</BaseBadge>
        <BaseBadge purpose="accent">Accent</BaseBadge>
        <BaseBadge purpose="success">Success</BaseBadge>
        <BaseBadge purpose="danger">Danger</BaseBadge>
        <BaseBadge purpose="warning">Warning</BaseBadge>
        <BaseBadge purpose="info">Info</BaseBadge>
      </div>
    `,
  }),
}

export const Shapes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different shape variants including the special octostar shape.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge, Star },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge shape="rounded">Rounded</BaseBadge>
        <BaseBadge shape="pill">Pill Shape</BaseBadge>
        <BaseBadge shape="square">Square</BaseBadge>
        <BaseBadge shape="octostar" size="md">
          <Star :size="16" />
        </BaseBadge>
      </div>
    `,
  }),
}

export const WithIcons: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Badges with various icons demonstrating icon integration.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge, Star, Heart, Bell, User, Settings, Mail },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge purpose="warning">
          <Star :size="14" />
          Starred
        </BaseBadge>
        <BaseBadge purpose="danger">
          <Heart :size="14" />
          Liked
        </BaseBadge>
        <BaseBadge purpose="info">
          <Bell :size="14" />
          3
        </BaseBadge>
        <BaseBadge purpose="success" shape="rounded">
          <User :size="14" />
        </BaseBadge>
        <BaseBadge purpose="secondary">
          <Settings :size="14" />
          Settings
        </BaseBadge>
        <BaseBadge purpose="primary">
          <Mail :size="14" />
          12
        </BaseBadge>
      </div>
    `,
  }),
}

export const Numbers: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Badges displaying numbers, commonly used for counts and notifications.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge purpose="danger" size="xs">1</BaseBadge>
        <BaseBadge purpose="danger" size="sm">5</BaseBadge>
        <BaseBadge purpose="danger" size="md">12</BaseBadge>
        <BaseBadge purpose="danger" size="lg">999</BaseBadge>
        <BaseBadge purpose="info" shape="pill">42</BaseBadge>
        <BaseBadge purpose="success" variant="outline">100%</BaseBadge>
      </div>
    `,
  }),
}

export const Closeable: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Badges with close functionality, useful for tags and removable items.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    setup() {
      const handleClose = (label: string) => {
        alert(`Closed: ${label}`)
      }
      return { handleClose }
    },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge :closeable="true" purpose="primary" @close="handleClose('Tag 1')">
          Tag 1
        </BaseBadge>
        <BaseBadge :closeable="true" purpose="secondary" @close="handleClose('Tag 2')">
          Tag 2
        </BaseBadge>
        <BaseBadge :closeable="true" purpose="success" @close="handleClose('Complete')">
          Complete
        </BaseBadge>
        <BaseBadge :closeable="true" purpose="warning" variant="outline" @close="handleClose('Warning')">
          Warning
        </BaseBadge>
      </div>
    `,
  }),
}

export const Outlined: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Badges with outlined style (transparent background with colored border).',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge :outlined="true" purpose="primary">Primary</BaseBadge>
        <BaseBadge :outlined="true" purpose="secondary">Secondary</BaseBadge>
        <BaseBadge :outlined="true" purpose="success">Success</BaseBadge>
        <BaseBadge :outlined="true" purpose="danger">Error</BaseBadge>
        <BaseBadge :outlined="true" purpose="warning">Warning</BaseBadge>
        <BaseBadge :outlined="true" purpose="info">Info</BaseBadge>
      </div>
    `,
  }),
}

export const StatusIndicators: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Common status indicator patterns using badges.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    template: `
      <div style="display: flex; flex-direction: column; gap: 1rem;">
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span>User Status:</span>
          <BaseBadge purpose="success" size="xs" shape="rounded">Online</BaseBadge>
        </div>
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span>Order Status:</span>
          <BaseBadge purpose="warning" size="sm" variant="outline">Pending</BaseBadge>
        </div>
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span>Task Priority:</span>
          <BaseBadge purpose="danger" size="xs">High</BaseBadge>
        </div>
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span>Notification Count:</span>
          <BaseBadge purpose="info" size="xs" shape="rounded">3</BaseBadge>
        </div>
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span>Version:</span>
          <BaseBadge purpose="secondary" variant="ghost" size="sm">v2.1.0</BaseBadge>
        </div>
      </div>
    `,
  }),
}

export const CompactMode: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrates compact mode with single characters and icons, which become perfect circles.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge, Star, Heart, User },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseBadge size="xs">A</BaseBadge>
        <BaseBadge size="sm">1</BaseBadge>
        <BaseBadge size="md">!</BaseBadge>
        <BaseBadge size="lg">?</BaseBadge>
        <BaseBadge size="sm" purpose="success"><Star :size="14" /></BaseBadge>
        <BaseBadge size="md" purpose="danger"><Heart :size="16" /></BaseBadge>
        <BaseBadge size="lg" purpose="info"><User :size="20" /></BaseBadge>
      </div>
    `,
  }),
}

export const InteractiveExample: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Interactive example demonstrating closeable badges with state management.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge },
    setup() {
      const tags = ['JavaScript', 'Vue.js', 'TypeScript', 'Storybook', 'CSS']
      const visibleTags = ref([...tags])

      const removeTag = (tagToRemove: string) => {
        visibleTags.value = visibleTags.value.filter(
          (tag: string) => tag !== tagToRemove,
        )
      }

      const resetTags = () => {
        visibleTags.value = [...tags]
      }

      return {
        visibleTags,
        removeTag,
        resetTags,
      }
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 1rem; max-width: 400px;">
        <h4>Skills (click × to remove):</h4>
        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; min-height: 2rem;">
          <BaseBadge
            v-for="tag in visibleTags"
            :key="tag"
            :closeable="true"
            purpose="primary"
            size="sm"
            @close="removeTag(tag)"
          >
            {{ tag }}
          </BaseBadge>
          <span v-if="visibleTags.length === 0" style="color: #666; font-style: italic;">
            No skills selected
          </span>
        </div>
        <button
          @click="resetTags"
          style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; align-self: flex-start;"
        >
          Reset Tags
        </button>
      </div>
    `,
  }),
}

export const AllCombinations: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive overview showing various combinations of props.',
      },
    },
  },
  render: () => ({
    components: { BaseBadge, Star },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem;">
        <div>
          <h4>Solid Variants</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
            <BaseBadge variant="solid" purpose="primary">Primary</BaseBadge>
            <BaseBadge variant="solid" purpose="secondary">Secondary</BaseBadge>
            <BaseBadge variant="solid" purpose="success">Success</BaseBadge>
            <BaseBadge variant="solid" purpose="danger">Danger</BaseBadge>
            <BaseBadge variant="solid" purpose="warning">Warning</BaseBadge>
            <BaseBadge variant="solid" purpose="info">Info</BaseBadge>
          </div>
        </div>

        <div>
          <h4>Outlined Variants</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
            <BaseBadge :outlined="true" purpose="primary">Primary</BaseBadge>
            <BaseBadge :outlined="true" purpose="secondary">Secondary</BaseBadge>
            <BaseBadge :outlined="true" purpose="success">Success</BaseBadge>
            <BaseBadge :outlined="true" purpose="danger">Danger</BaseBadge>
            <BaseBadge :outlined="true" purpose="warning">Warning</BaseBadge>
            <BaseBadge :outlined="true" purpose="info">Info</BaseBadge>
          </div>
        </div>

        <div>
          <h4>Special Shapes</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center;">
            <BaseBadge shape="pill" purpose="primary">Pill Shape</BaseBadge>
            <BaseBadge shape="square" purpose="secondary" size="md">Square</BaseBadge>
            <BaseBadge shape="octostar" purpose="warning" size="lg">
              <Star :size="20" />
            </BaseBadge>
          </div>
        </div>

        <div>
          <h4>Mixed Usage</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center;">
            <BaseBadge :closeable="true" purpose="primary" size="lg">Large Closeable</BaseBadge>
            <BaseBadge :outlined="true" shape="pill" purpose="success">Outlined Pill</BaseBadge>
            <BaseBadge variant="ghost" purpose="info" size="xs">Ghost XS</BaseBadge>
            <BaseBadge variant="dark" :closeable="true" purpose="accent">Dark Closeable</BaseBadge>
          </div>
        </div>
      </div>
    `,
  }),
}
