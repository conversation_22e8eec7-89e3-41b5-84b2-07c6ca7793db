<script setup lang="ts">
import { computed } from 'vue'
import { useSystemStyles, type StyleVariant, type StylePurpose } from '@/composables/useSystemStyles'
import LoadingSpinner from './LoadingSpinner.vue'

type ButtonSize = 'tiny' | 'compact' | 'default' | 'large'
type ButtonShape = 'rounded' | 'pill' | 'square'
type ButtonType = 'button' | 'submit' | 'reset' | undefined

type ButtonProps = {
  size?: ButtonSize
  variant?: StyleVariant
  purpose?: StylePurpose
  shape?: ButtonShape
  icon?: boolean
  disabled?: boolean
  loading?: boolean
  type?: ButtonType
  pill?: boolean
  test?: boolean
  shadow?: boolean
}

const props = withDefaults(defineProps<ButtonProps>(), {
  size: 'default',
  variant: 'solid',
  purpose: 'default',
  shape: 'rounded',
  icon: false,
  disabled: false,
  loading: false,
  type: 'button',
  pill: false,
  test: false,
  shadow: true
})

// Make variant and purpose reactive
const buttonVariant = computed(() => {
  return props.variant
})
const buttonPurpose = computed(() => props.purpose)

const { currentStyles } = useSystemStyles(buttonVariant.value, buttonPurpose.value)

const {
  background,
  border,
  text
} = currentStyles.value

if (props.test) {
  // Show debugging here when test prop is true
  console.log('Purpose', props.purpose)
}


const buttonClasses = computed(() => [
  `base-button--${props.size}`,
  !props.pill ? `base-button--${props.shape}` : '',
  {
    'base-button--icon': props.icon,
    'base-button--loading': props.loading,
    'base-button--disabled': props.disabled,
    'base-button--pill': props.pill,
    'level-0': props.size === 'tiny' || props.size === 'compact',
    'level-1': props.size === 'default',
    'level-2': props.size === 'large',
    'no-shadow': props.shadow === false
  }
])
</script>

<template>
  <button class="base-button shadow" :class="buttonClasses" :type="type" :disabled="disabled || loading">
    <!-- <span v-if="loading" class="loader"></span> -->
    <LoadingSpinner v-if="loading" />
    <span v-else class="button-content">
      <slot></slot>
    </span>
  </button>
</template>

<style scoped>
.base-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-gap-xs);
  cursor: pointer;
  border-width: calc(var(--unit) * 0.25);
  border-style: solid;
  padding: .25em .66em;
  transition:
    color var(--transition-in),
    background-color var(--transition-in),
    filter var(--transition-in),
    transform var(--transition-in);

  /* Dynamic styles from useSystemStyles */
  border-color: v-bind(border);
  background-color: v-bind(background);
  color: v-bind(text);

  .button-content {
    display: flex;
    align-items: center;
    gap: var(--space-gap-xs);
  }

  &:hover:not(:disabled),
  &:focus-visible:not(:disabled) {
    filter: brightness(1.25);
    scale: 1.02;
    transition:
      scale var(--transition-out),
      filter var(--transition-out);
  }

  &:active:not(:disabled) {
    filter: brightness(0.95);
    scale: 1;
    box-shadow: none;
    transition:
      scale var(--transition-click-out),
      filter var(--transition-click-out);
  }

  &.no-shadow {
    box-shadow: none;
  }
}

/* Size Variants */
.base-button--tiny {
  font-size: var(--step--2);
}

.base-button--compact {
  font-size: var(--step--1);
}

.base-button--default {
  font-size: var(--step-0);
}

.base-button--large {
  font-size: var(--step-1);
}

/* Icon Only Buttons */
.base-button--icon {
  aspect-ratio: 1;
  padding: .5em;
}

.base-button--icon.base-button--tiny {
  padding: .25em;
}

.base-button--icon.base-button--compact {
  padding: .33em;
}

.base-button--icon.base-button--large {
  padding: .75em;
}

/* Disabled State */
.base-button--disabled,
.base-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(20%);
  transition:
    color var(--transition-out),
    background-color var(--transition-out),
    filter var(--transition-out);
}

/* Loading State */
.base-button--loading {
  cursor: wait;
  transition:
    color var(--transition-out),
    background-color var(--transition-out),
    filter var(--transition-out);
}

.loader {
  width: calc(var(--unit) * 4);
  height: calc(var(--unit) * 4);
  border: calc(var(--unit) * 0.5) solid;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Focus State */
.base-button:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Shape Variants */
.base-button--rounded {
  border-radius: var(--radius-sm);
}

.base-button--rounded.base-button--tiny,
.base-button--rounded.base-button--compact {
  border-radius: var(--radius-xs);
}

.base-button--rounded.base-button--large {
  border-radius: var(--radius-m);
}

/* Pill Style */
.base-button--pill {
  border-radius: 100vw;
}

/* Icon button with pill */
.base-button--icon.base-button--pill {
  border-radius: 50%;
}

/* Icon button adjustments */
.base-button--icon.base-button--rounded,
.base-button--icon.base-button--pill {
  border-radius: 50%;
}

.base-button--icon.base-button--square {
  border-radius: 0;
}
</style>
