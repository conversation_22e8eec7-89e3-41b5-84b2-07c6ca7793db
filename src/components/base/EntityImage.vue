<script setup lang="ts">
import { User } from 'lucide-vue-next'
import { ref, computed } from 'vue'

type EntityImageProps = {
  publicId: string
  alt: string
  height?: number | string
  width?: number | string
  fit?: boolean
  aspectRatio?: string
  rounded?: boolean
}

const props = withDefaults(defineProps<EntityImageProps>(), {
  height: 'auto',
  width: '100%',
  fit: false,
  aspectRatio: '16/9',
  rounded: false
})

const failed = ref(false)
const emit = defineEmits(['load', 'error'])

const containerStyle = computed(() => ({
  aspectRatio: props.aspectRatio,
  borderRadius: props.rounded ? 'var(--radius-m)' : undefined
}))

const imageStyle = computed(() => ({
  objectFit: props.fit ? 'cover' : 'contain'
}))

const throwError = (event: Event) => {
  emit('error', event)
  failed.value = true
}
</script>

<template>
  <div class="entity-image" :style="containerStyle">
    <div class="entity-image__placeholder" v-if="!publicId">
      <slot name="placeholder">
        <div class="entity-image__no-image">No image available</div>
      </slot>
    </div>
    <CloudImage v-else-if="!failed" :publicId="publicId" :alt="alt" :height="height" :width="width" :style="imageStyle"
      class="entity-image__img" @load="emit('load')" @error="throwError" />
    <div v-else class="entity-image__placeholder">
      <slot name="error">
        <div class="entity-image__no-image" :title="`Image '${publicId}' failed to load`">
          <User class="entity-image__icon" />
          <span>Image unavailable</span>
          <!-- <small>{{ publicId }}</small> -->
        </div>
      </slot>
    </div>
    <slot />
  </div>
</template>

<style scoped>
.entity-image {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.entity-image__img {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.entity-image:hover .entity-image__img {
  transform: scale(1.05);
}

.entity-image__placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.entity-image__no-image {
  display: grid;
  justify-content: center;
  justify-items: center;
  gap: var(--space-xs);

  .entity-image__icon {
    width: 3rem;
    height: 3rem;
    color: var(--color-text-muted);
  }
}
</style>
