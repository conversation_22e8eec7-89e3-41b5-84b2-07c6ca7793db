<script setup lang="ts">
import { computed, ref } from 'vue'
import { useSystemStyles, type StyleVariant, type StylePurpose } from '@/composables/useSystemStyles'
import { X } from 'lucide-vue-next'

type Props = {
  variant?: StyleVariant
  purpose?: StylePurpose
  size?: 'xs' | 'sm' | 'md' | 'lg'
  shape?: 'rounded' | 'pill' | 'square' | 'octostar'
  closeable?: boolean
  outlined?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'solid',
  purpose: 'primary',
  size: 'sm',
  shape: 'rounded',
  closeable: false,
  outlined: false
})

defineEmits(['close'])

// Check if content is short (<= 2 characters or 1 icon)
const isCompact = ref(false)

const { currentStyles } = useSystemStyles(props.variant, props.purpose)

const badgeClasses = computed(() => [
  `badge-${props.size}`,
  `badge-${props.shape}`,
  {
    'badge-compact': isCompact.value
  }
])

const badgeStyles = computed(() => {
  const bgColor = props.outlined ? 'transparent' : currentStyles.value.background

  return {
    backgroundColor: bgColor,
    borderColor: currentStyles.value.border,
    color: currentStyles.value.text
  }
})
</script>

<template>
  <span class="badge" :class="badgeClasses" :style="badgeStyles">
    <slot>O</slot>
    <X v-if="closeable" class="icon" @click="$emit('close')" role="button" tabindex="0" aria-label="Remove"
      @keydown.enter="$emit('close')" @keydown.space.prevent="$emit('close')" />
  </span>
</template>

<style scoped>
.badge {
  display: inline-flex;
  gap: var(--space-2xs);
  align-items: center;
  justify-content: center;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
  vertical-align: middle;
  border: 1px solid;
  transition: all 0.2s ease;
}

/* Size Variants */
.badge-xs {
  font-size: var(--step--3);
  padding: var(--space-3xs) var(--space-2xs);
  min-height: 1.25rem;
}

.badge-sm {
  font-size: var(--step--2);
  padding: var(--space-2xs) var(--space-xs);
  min-height: 1.5rem;
}

.badge-md {
  font-size: var(--step--1);
  padding: var(--space-xs) var(--space-s);
  min-height: 1.75rem;
}

.badge-lg {
  font-size: var(--step-0);
  padding: var(--space-s) var(--space-m);
  min-height: 2rem;
}

/* Compact Variants - Perfect circles for single characters or icons */
.badge-compact.badge-xs {
  width: 1.25rem;
  padding: 0;
}

.badge-compact.badge-sm {
  width: 1.5rem;
  padding: 0;
}

.badge-compact.badge-md {
  width: 1.75rem;
  padding: 0;
}

.badge-compact.badge-lg {
  width: 2rem;
  padding: 0;
}

/* Shape Variants */
.badge-rounded {
  border-radius: 100vw;
}

.badge-compact {
  border-radius: 50%;
  aspect-ratio: 1;
}

.badge-octostar {
  aspect-ratio: 1;
  padding: .4em;
  clip-path: polygon(30% 0%, 50% 10%, 70% 0%, 80% 20%, 100% 30%, 90% 50%, 100% 70%, 80% 80%, 70% 100%, 50% 90%, 30% 100%, 20% 80%, 0% 70%, 10% 50%, 0% 30%, 20% 20%);
}

.badge:not(.badge-rounded):not(.badge-compact):not(.badge-octostar) {
  border-radius: var(--radius-sm);
}

.icon {
  box-sizing: border-box;
  padding: .1em;
  background-color: var(--color-danger);
  color: var(--fg-soft);
  border-radius: 50%;
  cursor: pointer;
}
</style>
