<script setup lang="ts">
type Props = {
  title?: string
  minimal?: boolean
}

withDefaults(defineProps<Props>(), {
  title: '',
  minimal: false
})
</script>

<template>
  <section class="layer section shadow" :class="{ minimal }">
    <slot name="header">
      <h2 v-if="title" class="base-section-title">{{ title }}</h2>
    </slot>
    <slot></slot>
    <footer v-if="$slots.footer" class="layer card-inner card__footer">
      <slot name="footer" />
    </footer>
  </section>
</template>

<style scoped>
.section {
  display: grid;
  gap: var(--space-m);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-l);
  background-color: var(--color-bg-1);
  padding: var(--space-s) var(--space-m);

  &.minimal {
    padding: 0;
    border: none;
    background-color: var(--color-bg-0);
    box-shadow: none;
  }
}

.base-section-title {
  padding-inline-start: .25em;
  font-size: var(--step-1);
}
</style>
