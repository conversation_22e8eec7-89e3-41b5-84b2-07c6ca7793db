<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps<{
  size?: 'tiny' | 'compact' | 'default' | 'large'
}>()

const size = computed(() => {
  switch (props.size) {
    case 'tiny':
      return '.5em'
    case 'compact':
      return '.8em'
    case 'default':
      return '1em'
    case 'large':
      return '2em'
    default:
      return '1em'
  }
})

</script>

<template>
  <div class="loading-spinner">
    <div class="spinner"></div>
    <span v-if="$slots.default" class="spinner-text">
      <slot></slot>
    </span>
  </div>
</template>

<style scoped>
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: var(--color-text);
  font-size: v-bind(size);
}

.spinner {
  width: 1em;
  height: 1em;
  border: 4px solid var(--color-text-muted);
  border-top-color: var(--color-text);
  border-left-color: var(--color-text-soft);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-text {
  font-size: var(--step--1);
  color: var(--color-text-muted);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
