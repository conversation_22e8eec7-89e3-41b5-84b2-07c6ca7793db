<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import SearchSelectDropdown from '@/components/base/SearchSelectDropdown.vue'

type Option = {
  value: string
  label: string
  searchData?: string[]
}

type Props = {
  modelValue?: string | null
  options: Option[]
  placeholder?: string
  disabled?: boolean
  label?: string
  required?: boolean
  clearable?: boolean
  clearOnClickAway?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Search...',
  disabled: false,
  clearOnClickAway: false,
  label: '',
  required: false,
  clearable: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string | null]
  'change': [value: string | null]
}>()

const isOpen = ref(false)
const searchQuery = ref('')
const dropdownRef = ref<HTMLElement | null>(null)
const inputRef = ref<HTMLInputElement | null>(null)
const highlightedIndex = ref(0)

const filteredOptions = computed(() => {
  if (!searchQuery.value) return props.options

  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option => {
    const matchLabel = option.label.toLowerCase().includes(query)
    const matchSearchData = option.searchData?.some(data =>
      data?.toLowerCase().includes(query)
    )
    return matchLabel || matchSearchData
  })
})

const selectedOption = computed(() =>
  props.options.find(option => option.value === props.modelValue)
)

// Reset highlighted index when filtered options change
watch(filteredOptions, () => {
  highlightedIndex.value = 0
})

watch(() => props.modelValue, (newValue) => {
  const option = props.options.find(opt => opt.value === newValue)
  if (option) {
    searchQuery.value = option.label
  } else {
    searchQuery.value = ''
  }
}, { immediate: true })

watch(() => props.options, () => {
  if (props.modelValue) {
    const option = props.options.find(opt => opt.value === props.modelValue)
    if (option) {
      searchQuery.value = option.label
    }
  }
}, { immediate: true, deep: true })

function handleSelect(option: Option) {
  emit('change', option.value)
  searchQuery.value = option.label
  isOpen.value = false
  highlightedIndex.value = 0
}

function handleUpdateModelValue(value: string) {
  emit('update:modelValue', value)
}

function handleInput(event: Event) {
  const value = (event.target as HTMLInputElement).value
  searchQuery.value = value
  isOpen.value = true
}

function handleFocus() {
  isOpen.value = true
}

function handleClickOutside(event: MouseEvent) {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false

    if (props.clearOnClickAway && searchQuery.value !== selectedOption.value?.label) {
      // Clear selection if search query doesn't match selected option
      emit('update:modelValue', '')
      emit('change', '')
      searchQuery.value = ''
    } else {
      // Reset to selected option or empty
      searchQuery.value = selectedOption.value?.label || ''
    }

    highlightedIndex.value = 0
  }
}


function highlightNextOption() {
  highlightedIndex.value = Math.min(
    highlightedIndex.value + 1,
    filteredOptions.value.length - 1
  )
  scrollHighlightedIntoView()
}

function highlightPreviousOption() {
  highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0)
  scrollHighlightedIntoView()
}

function handleKeydown(event: KeyboardEvent) {
  if (props.disabled) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      if (!isOpen.value) {
        isOpen.value = true
      } else {
        highlightNextOption()
      }
      break

    case 'ArrowUp':
      event.preventDefault()
      if (isOpen.value) {
        highlightPreviousOption()
      }
      break

    case 'Enter':
      event.preventDefault()
      if (isOpen.value && highlightedIndex.value < filteredOptions.value.length) {
        handleSelect(filteredOptions.value[highlightedIndex.value])
      }
      break

    case 'Escape':
      event.preventDefault()
      isOpen.value = false
      searchQuery.value = selectedOption.value?.label || ''
      highlightedIndex.value = 0
      inputRef.value?.blur()
      break

    case 'Tab':
      if (isOpen.value) {
        isOpen.value = false
        searchQuery.value = selectedOption.value?.label || ''
      }
      break
  }
}

function scrollHighlightedIntoView() {
  nextTick(() => {
    const highlighted = dropdownRef.value?.querySelector('.search-select__option--highlighted')
    if (highlighted) {
      highlighted.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      })
    }
  })
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  if (selectedOption.value) {
    searchQuery.value = selectedOption.value.label
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const id = computed(() => `search-select-${Math.random().toString(36).substr(2, 9)}`)

// Add clear handler function
function handleClear(event: Event) {
  event.preventDefault()
  event.stopPropagation()
  emit('update:modelValue', null)
  emit('change', null)
  searchQuery.value = ''
  isOpen.value = false
}
</script>

<template>
  <div class="search-select-wrapper">
    <label v-if="label" :for="id" class="search-select__label">
      {{ label }}
      <span v-if="required" class="required-indicator">*</span>
    </label>

    <div class="search-select" ref="dropdownRef" role="combobox" :aria-expanded="isOpen" aria-haspopup="listbox">
      <div class="search-select__input-wrapper">
        <input :id="id" ref="inputRef" type="text" v-model="searchQuery" :placeholder="placeholder" :disabled="disabled"
          :aria-activedescendant="highlightedIndex >= 0 ? `option-${filteredOptions[highlightedIndex]?.value}` : undefined"
          class="search-select__input" @input="handleInput" @focus="handleFocus" @keydown="handleKeydown"
          aria-autocomplete="list">
        <button v-if="clearable && modelValue && !disabled" type="button" class="search-select__clear-button"
          @click="handleClear" tabindex="-1" aria-label="Clear selection">
          ×
        </button>
      </div>

      <SearchSelectDropdown :isOpen :options="filteredOptions" :modelValue="modelValue || ''" :highlightedIndex
        @update:highlightedIndex="highlightedIndex = $event" @update:isOpen="isOpen = $event"
        @update:modelValue="handleUpdateModelValue" @change="handleSelect" />
    </div>
  </div>
</template>

<style scoped>
.search-select-wrapper {
  display: grid;
  gap: 0.5rem;
}

.search-select__label {
  font-size: var(--step--1);
  font-weight: 500;
  color: var(--color-text);
}

.required-indicator {
  color: var(--color-error);
  margin-left: 0.25rem;
}

.search-select {
  position: relative;
  width: 100%;
}

.search-select__input-wrapper {
  position: relative;
  width: 100%;
}

.search-select__input {
  width: 100%;
  padding: 0.5rem 1rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  font-size: inherit;
  transition: all 0.2s ease;
  padding-right: 2rem;
}

.search-select__input:hover:not(:disabled) {
  border-color: var(--color-border-hover);
}

.search-select__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.search-select__input:disabled {
  background-color: var(--color-background-alt);
  cursor: not-allowed;
  opacity: 0.7;
}

.search-select__clear-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 1.2em;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.search-select__clear-button:hover {
  color: var(--color-text);
  background-color: var(--color-border);
}

.search-select__clear-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}
</style>
