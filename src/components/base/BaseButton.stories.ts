import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3-vite'
import { ref } from 'vue'
import {
  Play,
  Pause,
  Download,
  Settings,
  Heart,
  User,
  Plus,
  Edit,
  Trash2,
  Save,
  Send,
  Search,
} from 'lucide-vue-next'
import BaseButton from '@/components/base/BaseButton.vue'

const meta: Meta<typeof BaseButton> = {
  title: 'Base/BaseButton',
  component: BaseButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# BaseButton

A highly customizable button component with consistent styling and multiple variants.

## Features

- **Multiple Sizes**: tiny, compact, default, large variants
- **Style Variants**: solid, outline, ghost, and dark styles
- **Purpose Colors**: default, primary, secondary, accent, success, danger, warning, and info
- **Shape Options**: rounded, pill, and square variants
- **Icon Support**: Dedicated icon-only buttons with proper sizing
- **Loading State**: Built-in loading spinner functionality
- **Accessibility**: Full keyboard navigation support and semantic HTML
- **Interaction Effects**: Hover, focus, and active state animations

## Usage

\`\`\`vue
<BaseButton variant="solid" purpose="primary" size="default">
  Click Me
</BaseButton>

<BaseButton :icon="true" purpose="success">
  <Plus :size="16" />
</BaseButton>

<BaseButton :loading="true" purpose="primary">
  Processing...
</BaseButton>
\`\`\`

## Props

- **size**: 'tiny' | 'compact' | 'default' | 'large' (default: 'default')
- **variant**: 'solid' | 'outline' | 'ghost' | 'dark' (default: 'solid')
- **purpose**: 'default' | 'primary' | 'secondary' | 'accent' | 'success' | 'danger' | 'warning' | 'info' (default: 'default')
- **shape**: 'rounded' | 'pill' | 'square' (default: 'rounded')
- **icon**: boolean - Makes the button square for icon-only usage (default: false)
- **disabled**: boolean (default: false)
- **loading**: boolean - Shows loading spinner (default: false)
- **type**: 'button' | 'submit' | 'reset' (default: 'button')
- **pill**: boolean - Overrides shape to be pill-shaped (default: false)
- **shadow**: boolean - Adds drop shadow (default: true)
- **test**: boolean - Enables debug console logging (default: false)
        `,
      },
      canvas: {
        sourceState: 'shown',
      },
    },
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['tiny', 'compact', 'default', 'large'],
      description: 'Size of the button',
    },
    variant: {
      control: 'select',
      options: ['solid', 'outline', 'ghost', 'dark'],
      description: 'Visual style variant of the button',
    },
    purpose: {
      control: 'select',
      options: [
        'default',
        'primary',
        'secondary',
        'accent',
        'success',
        'danger',
        'warning',
        'info',
      ],
      description: 'Semantic purpose determining the color scheme',
    },
    shape: {
      control: 'select',
      options: ['rounded', 'pill', 'square'],
      description: 'Shape variant of the button',
    },
    icon: {
      control: 'boolean',
      description: 'Whether the button is icon-only (makes it square)',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the button shows loading state',
    },
    type: {
      control: 'select',
      options: ['button', 'submit', 'reset'],
      description: 'HTML button type',
    },
    pill: {
      control: 'boolean',
      description: 'Whether the button has pill shape (overrides shape prop)',
    },
    shadow: {
      control: 'boolean',
      description: 'Whether the button has drop shadow',
    },
    test: {
      control: 'boolean',
      description: 'Enable debug console logging',
    },
    default: {
      control: 'text',
      description: 'Default slot content',
    },
  },
  args: {
    size: 'default',
    variant: 'solid',
    purpose: 'default',
    shape: 'rounded',
    icon: false,
    disabled: false,
    loading: false,
    type: 'button',
    pill: false,
    shadow: true,
    test: false,
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'The basic button with default styling and text content.',
      },
    },
  },
  args: {
    default: 'Button',
  },
  render: args => ({
    components: { BaseButton },
    setup() {
      return { args }
    },
    template: `
      <BaseButton v-bind="args">
        {{ args.default }}
      </BaseButton>
    `,
  }),
}

export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different size variants of the button component.',
      },
    },
  },
  render: () => ({
    components: { BaseButton },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseButton size="tiny">Tiny</BaseButton>
        <BaseButton size="compact">Compact</BaseButton>
        <BaseButton size="default">Default</BaseButton>
        <BaseButton size="large">Large</BaseButton>
      </div>
    `,
  }),
}

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different visual style variants of the button.',
      },
    },
  },
  render: () => ({
    components: { BaseButton },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseButton variant="solid" purpose="primary">Solid</BaseButton>
        <BaseButton variant="outline" purpose="primary">Outline</BaseButton>
        <BaseButton variant="ghost" purpose="primary">Ghost</BaseButton>
        <BaseButton variant="dark" purpose="primary">Dark</BaseButton>
      </div>
    `,
  }),
}

export const Purposes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different semantic purposes showing various color schemes.',
      },
    },
  },
  render: () => ({
    components: { BaseButton },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseButton purpose="default">Default</BaseButton>
        <BaseButton purpose="primary">Primary</BaseButton>
        <BaseButton purpose="secondary">Secondary</BaseButton>
        <BaseButton purpose="accent">Accent</BaseButton>
        <BaseButton purpose="success">Success</BaseButton>
        <BaseButton purpose="danger">Danger</BaseButton>
        <BaseButton purpose="warning">Warning</BaseButton>
        <BaseButton purpose="info">Info</BaseButton>
      </div>
    `,
  }),
}

export const Shapes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different shape variants including pill and square options.',
      },
    },
  },
  render: () => ({
    components: { BaseButton },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseButton shape="rounded">Rounded</BaseButton>
        <BaseButton shape="pill">Pill Shape</BaseButton>
        <BaseButton shape="square">Square</BaseButton>
        <BaseButton :pill="true">Pill Prop</BaseButton>
      </div>
    `,
  }),
}

export const IconButtons: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Icon-only buttons with proper square dimensions and various sizes.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Play, Settings, Heart, User, Plus, Edit },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem;">
        <div>
          <h4>Icon Button Sizes</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton :icon="true" size="tiny" purpose="primary">
              <Plus :size="12" />
            </BaseButton>
            <BaseButton :icon="true" size="compact" purpose="primary">
              <Plus :size="14" />
            </BaseButton>
            <BaseButton :icon="true" size="default" purpose="primary">
              <Plus :size="16" />
            </BaseButton>
            <BaseButton :icon="true" size="large" purpose="primary">
              <Plus :size="20" />
            </BaseButton>
          </div>
        </div>

        <div>
          <h4>Icon Button Variants</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton :icon="true" variant="solid" purpose="primary">
              <Play :size="16" />
            </BaseButton>
            <BaseButton :icon="true" variant="outline" purpose="secondary">
              <Settings :size="16" />
            </BaseButton>
            <BaseButton :icon="true" variant="ghost" purpose="success">
              <Heart :size="16" />
            </BaseButton>
            <BaseButton :icon="true" variant="dark" purpose="info">
              <User :size="16" />
            </BaseButton>
          </div>
        </div>

        <div>
          <h4>Icon Button Shapes</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton :icon="true" shape="rounded" purpose="primary">
              <Edit :size="16" />
            </BaseButton>
            <BaseButton :icon="true" shape="square" purpose="secondary">
              <Settings :size="16" />
            </BaseButton>
            <BaseButton :icon="true" :pill="true" purpose="success">
              <Heart :size="16" />
            </BaseButton>
          </div>
        </div>
      </div>
    `,
  }),
}

export const WithIcons: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Regular buttons with icons and text content.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Download, Save, Send, Search, Trash2 },
    template: `
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <BaseButton purpose="primary">
          <Download :size="16" />
          Download
        </BaseButton>
        <BaseButton purpose="success">
          <Save :size="16" />
          Save
        </BaseButton>
        <BaseButton purpose="info">
          <Send :size="16" />
          Send
        </BaseButton>
        <BaseButton purpose="secondary">
          <Search :size="16" />
          Search
        </BaseButton>
        <BaseButton purpose="danger" variant="outline">
          <Trash2 :size="16" />
          Delete
        </BaseButton>
      </div>
    `,
  }),
}

export const States: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different button states including disabled and loading.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Save, Send },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem;">
        <div>
          <h4>Normal vs Disabled</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton purpose="primary">Normal Button</BaseButton>
            <BaseButton purpose="primary" :disabled="true">Disabled Button</BaseButton>
            <BaseButton purpose="secondary" variant="outline">Normal Outline</BaseButton>
            <BaseButton purpose="secondary" variant="outline" :disabled="true">Disabled Outline</BaseButton>
          </div>
        </div>

        <div>
          <h4>Loading States</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton purpose="primary" :loading="true">Loading...</BaseButton>
            <BaseButton purpose="success" :loading="true">
              <Save :size="16" />
              Saving
            </BaseButton>
            <BaseButton purpose="info" variant="outline" :loading="true">Processing</BaseButton>
            <BaseButton :icon="true" purpose="primary" :loading="true">
              <Send :size="16" />
            </BaseButton>
          </div>
        </div>
      </div>
    `,
  }),
}

export const ButtonTypes: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different HTML button types for form usage.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Save, Send },
    template: `
      <form style="display: flex; flex-direction: column; gap: 1rem; max-width: 300px; padding: 1rem; border: 1px solid #ddd; border-radius: 8px;">
        <h4>Form Example</h4>
        <input type="text" placeholder="Enter your name" style="padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;" />
        <input type="email" placeholder="Enter your email" style="padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;" />

        <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
          <BaseButton type="submit" purpose="primary">
            <Send :size="16" />
            Submit
          </BaseButton>
          <BaseButton type="reset" purpose="secondary" variant="outline">
            Reset
          </BaseButton>
          <BaseButton type="button" purpose="danger" variant="ghost">
            Cancel
          </BaseButton>
        </div>
      </form>
    `,
  }),
}

export const NoShadow: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Buttons with shadow disabled for flat design aesthetics.',
      },
    },
  },
  render: () => ({
    components: { BaseButton },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem;">
        <div>
          <h4>With Shadow (Default)</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton purpose="primary">With Shadow</BaseButton>
            <BaseButton purpose="secondary">With Shadow</BaseButton>
            <BaseButton purpose="success">With Shadow</BaseButton>
          </div>
        </div>

        <div>
          <h4>Without Shadow</h4>
          <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <BaseButton purpose="primary" :shadow="false">No Shadow</BaseButton>
            <BaseButton purpose="secondary" :shadow="false">No Shadow</BaseButton>
            <BaseButton purpose="success" :shadow="false">No Shadow</BaseButton>
          </div>
        </div>
      </div>
    `,
  }),
}

export const InteractiveExample: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Interactive example demonstrating button states and user interactions.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Play, Pause, Heart },
    setup() {
      const isPlaying = ref(false)
      const isLiked = ref(false)
      const likeCount = ref(42)
      const isLoading = ref(false)

      const togglePlay = () => {
        isPlaying.value = !isPlaying.value
      }

      const toggleLike = () => {
        if (isLoading.value) return

        isLoading.value = true

        // Simulate API call
        setTimeout(() => {
          isLiked.value = !isLiked.value
          likeCount.value += isLiked.value ? 1 : -1
          isLoading.value = false
        }, 1000)
      }

      return {
        isPlaying,
        isLiked,
        likeCount,
        isLoading,
        togglePlay,
        toggleLike,
      }
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem; max-width: 400px;">
        <h4>Media Player Controls</h4>

        <div style="display: flex; align-items: center; gap: 1rem;">
          <BaseButton
            :icon="true"
            purpose="primary"
            size="large"
            @click="togglePlay"
          >
            <Play v-if="!isPlaying" :size="20" />
            <Pause v-else :size="20" />
          </BaseButton>

          <BaseButton
            :purpose="isLiked ? 'danger' : 'secondary'"
            :variant="isLiked ? 'solid' : 'outline'"
            :loading="isLoading"
            @click="toggleLike"
            :disabled="isLoading"
          >
            <Heart :size="16" />
            {{ likeCount }}
          </BaseButton>
        </div>

        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; font-size: 0.9rem;">
          <strong>State:</strong>
          <ul>
            <li>Playing: {{ isPlaying ? 'Yes' : 'No' }}</li>
            <li>Liked: {{ isLiked ? 'Yes' : 'No' }}</li>
            <li>Like Count: {{ likeCount }}</li>
            <li>Loading: {{ isLoading ? 'Yes' : 'No' }}</li>
          </ul>
        </div>
      </div>
    `,
  }),
}

export const AllCombinations: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive overview showing various combinations of props.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Plus, Settings },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem;">
        <div>
          <h4>Solid Variants by Purpose</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
            <BaseButton variant="solid" purpose="primary">Primary</BaseButton>
            <BaseButton variant="solid" purpose="secondary">Secondary</BaseButton>
            <BaseButton variant="solid" purpose="success">Success</BaseButton>
            <BaseButton variant="solid" purpose="danger">Danger</BaseButton>
            <BaseButton variant="solid" purpose="warning">Warning</BaseButton>
            <BaseButton variant="solid" purpose="info">Info</BaseButton>
          </div>
        </div>

        <div>
          <h4>Outline Variants</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
            <BaseButton variant="outline" purpose="primary">Primary</BaseButton>
            <BaseButton variant="outline" purpose="secondary">Secondary</BaseButton>
            <BaseButton variant="outline" purpose="success">Success</BaseButton>
            <BaseButton variant="outline" purpose="danger">Danger</BaseButton>
            <BaseButton variant="outline" purpose="warning">Warning</BaseButton>
            <BaseButton variant="outline" purpose="info">Info</BaseButton>
          </div>
        </div>

        <div>
          <h4>Ghost Variants</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
            <BaseButton variant="ghost" purpose="primary">Primary</BaseButton>
            <BaseButton variant="ghost" purpose="secondary">Secondary</BaseButton>
            <BaseButton variant="ghost" purpose="success">Success</BaseButton>
            <BaseButton variant="ghost" purpose="danger">Danger</BaseButton>
            <BaseButton variant="ghost" purpose="warning">Warning</BaseButton>
            <BaseButton variant="ghost" purpose="info">Info</BaseButton>
          </div>
        </div>

        <div>
          <h4>Size and Shape Combinations</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center;">
            <BaseButton size="tiny" shape="pill" purpose="primary">Tiny Pill</BaseButton>
            <BaseButton size="compact" shape="square" purpose="secondary">Compact Square</BaseButton>
            <BaseButton size="large" :pill="true" purpose="success">Large Pill</BaseButton>
            <BaseButton :icon="true" size="large" shape="square" purpose="danger">
              <Settings :size="20" />
            </BaseButton>
          </div>
        </div>

        <div>
          <h4>Special Combinations</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center;">
            <BaseButton variant="dark" :pill="true" purpose="accent">Dark Pill</BaseButton>
            <BaseButton variant="ghost" :shadow="false" purpose="info">No Shadow Ghost</BaseButton>
            <BaseButton variant="outline" :disabled="true" purpose="warning">Disabled Outline</BaseButton>
            <BaseButton :icon="true" variant="ghost" :pill="true" purpose="success">
              <Plus :size="16" />
            </BaseButton>
          </div>
        </div>
      </div>
    `,
  }),
}

export const AccessibilityExample: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Accessibility features demonstration including keyboard navigation and semantic HTML.',
      },
    },
  },
  render: () => ({
    components: { BaseButton, Save, Send, Trash2 },
    template: `
      <div style="display: flex; flex-direction: column; gap: 2rem; max-width: 500px;">
        <h4>Accessibility Features</h4>

        <div style="background: #f0f8ff; padding: 1rem; border-radius: 4px; border-left: 4px solid #007bff;">
          <p><strong>Try these keyboard interactions:</strong></p>
          <ul>
            <li><kbd>Tab</kbd> - Navigate between buttons</li>
            <li><kbd>Enter</kbd> or <kbd>Space</kbd> - Activate button</li>
            <li><kbd>Shift + Tab</kbd> - Navigate backwards</li>
          </ul>
        </div>

        <div style="display: flex; flex-wrap: wrap; gap: 1rem;">
          <BaseButton purpose="primary" @click="() => alert('Save clicked!')">
            <Save :size="16" />
            Save Document
          </BaseButton>

          <BaseButton purpose="info" variant="outline" @click="() => alert('Send clicked!')">
            <Send :size="16" />
            Send Message
          </BaseButton>

          <BaseButton purpose="danger" variant="ghost" @click="() => alert('Delete clicked!')">
            <Trash2 :size="16" />
            Delete Item
          </BaseButton>

          <BaseButton purpose="secondary" :disabled="true">
            <Settings :size="16" />
            Disabled Action
          </BaseButton>
        </div>

        <div style="background: #fff3cd; padding: 1rem; border-radius: 4px; border-left: 4px solid #ffc107;">
          <p><strong>Accessibility Notes:</strong></p>
          <ul style="margin: 0; font-size: 0.9rem;">
            <li>All buttons have proper focus indicators</li>
            <li>Disabled buttons are not focusable</li>
            <li>Loading buttons prevent multiple activations</li>
            <li>Icon buttons maintain accessibility with proper sizing</li>
          </ul>
        </div>
      </div>
    `,
  }),
}
