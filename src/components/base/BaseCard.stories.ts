import type { <PERSON>a, StoryObj } from '@storybook/vue3-vite'
import {
  User,
  <PERSON>tings,
  Heart,
  Star,
  Calendar,
  MapPin,
  Clock,
  DollarSign,
  TrendingUp,
  AlertCircle,
} from 'lucide-vue-next'
import BaseCard from '@/components/base/BaseCard.vue'
import BaseButton from '@/components/base/BaseButton.vue'
import BaseBadge from '@/components/base/BaseBadge.vue'

const meta: Meta<typeof BaseCard> = {
  title: 'Base/BaseCard',
  component: BaseCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# BaseCard

A flexible card component for containing related information with optional header and footer sections.

## Features

- **Flexible Layout**: Supports header, content, and footer slots
- **Background Levels**: 5 different background levels (0-4) for visual hierarchy
- **Variants**: Default and outline styling options
- **Border Control**: Optional border styling
- **Shadow Support**: Optional drop shadow effect
- **Responsive**: Container query based responsive behavior
- **Semantic HTML**: Uses proper article element with header/footer

## Usage

\`\`\`vue
<BaseCard variant="default" :level="2" :shadow="true">
  <template #header>
    Card Title
  </template>

  <p>Card content goes here...</p>

  <template #footer>
    <BaseButton size="sm">Action</BaseButton>
  </template>
</BaseCard>
\`\`\`

## Props

- **variant**: 'default' | 'outline' (default: 'default')
- **border**: boolean - Adds explicit border (default: false)
- **shadow**: boolean - Adds drop shadow (default: false)
- **level**: number (0-4) - Background color level for visual hierarchy (default: 2)

## Slots

- **default**: Main content area
- **header**: Optional header section with h3 styling
- **footer**: Optional footer section, typically for actions
        `,
      },
      canvas: {
        sourceState: 'shown',
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'outline'],
      description: 'Visual style variant of the card',
    },
    border: {
      control: 'boolean',
      description: 'Whether the card has an explicit border',
    },
    shadow: {
      control: 'boolean',
      description: 'Whether the card has drop shadow',
    },
    level: {
      control: { type: 'range', min: 0, max: 4, step: 1 },
      description: 'Background color level (0-4) for visual hierarchy',
    },
  },
  args: {
    variant: 'default',
    border: false,
    shadow: false,
    level: 2,
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'A basic card with default styling and simple content.',
      },
    },
  },
  render: args => ({
    components: { BaseCard },
    setup() {
      return { args }
    },
    template: `
      <BaseCard v-bind="args" style="width: 300px;">
        <p>This is a simple card with some content. Cards are perfect for organizing related information in a clean, contained format.</p>
      </BaseCard>
    `,
  }),
}

export const WithHeader: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Card with a header section containing a title.',
      },
    },
  },
  render: () => ({
    components: { BaseCard },
    template: `
      <BaseCard style="width: 300px;">
        <template #header>
          Card Title
        </template>
        <p>This card has a header with a title. The header uses semantic HTML with an h3 element.</p>
      </BaseCard>
    `,
  }),
}

export const WithFooter: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Card with a footer section, typically used for actions.',
      },
    },
  },
  render: () => ({
    components: { BaseCard, BaseButton },
    template: `
      <BaseCard style="width: 300px;">
        <p>This card has a footer section with action buttons. The footer automatically sticks to the bottom of the card.</p>

        <template #footer>
          <div style="display: flex; gap: 0.5rem;">
            <BaseButton size="compact" purpose="primary">Accept</BaseButton>
            <BaseButton size="compact" variant="outline">Cancel</BaseButton>
          </div>
        </template>
      </BaseCard>
    `,
  }),
}

export const FullCard: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Complete card with header, content, and footer sections.',
      },
    },
  },
  render: () => ({
    components: { BaseCard, BaseButton, User },
    template: `
      <BaseCard style="width: 350px;">
        <template #header>
          <div style="display: flex; align-items: center; gap: 0.5rem;">
            <User :size="20" />
            User Profile
          </div>
        </template>

        <div style="display: flex; flex-direction: column; gap: 1rem;">
          <div>
            <h4 style="margin: 0 0 0.5rem 0;">John Doe</h4>
            <p style="margin: 0; color: var(--color-text-soft);">Software Developer</p>
          </div>

          <p style="margin: 0;">A passionate developer with 5+ years of experience in web technologies. Loves creating user-friendly applications.</p>
        </div>

        <template #footer>
          <div style="display: flex; gap: 0.5rem;">
            <BaseButton size="compact" purpose="primary">View Profile</BaseButton>
            <BaseButton size="compact" variant="outline">Message</BaseButton>
          </div>
        </template>
      </BaseCard>
    `,
  }),
}

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different visual variants of the card component.',
      },
    },
  },
  render: () => ({
    components: { BaseCard },
    template: `
      <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <BaseCard variant="default" style="width: 250px;">
          <template #header>Default Variant</template>
          <p>This is the default card variant with standard styling.</p>
        </BaseCard>

        <BaseCard variant="outline" style="width: 250px;">
          <template #header>Outline Variant</template>
          <p>This is the outline card variant with border emphasis.</p>
        </BaseCard>
      </div>
    `,
  }),
}

export const BackgroundLevels: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different background levels for visual hierarchy (0-4).',
      },
    },
  },
  render: () => ({
    components: { BaseCard },
    template: `
      <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <BaseCard :level="0" style="width: 200px;">
        <template #header>Level 0</template>
        <p>Background</p>
        <template #footer>Level 0</template>
        </BaseCard>

        <BaseCard :level="1" style="width: 200px;">
          <template #header>Level 1</template>
          <p>Back</p>
          <template #footer>Level 1</template>
        </BaseCard>

        <BaseCard :level="2" style="width: 200px;">
          <template #header>Level 2</template>
          <p>Middle</p>
          <template #footer>Level 2</template>
        </BaseCard>

        <BaseCard :level="3" style="width: 200px;">
          <template #header>Level 3</template>
          <p>Front</p>
          <template #footer>Level 3</template>
        </BaseCard>

        <BaseCard :level="4" style="width: 200px;">
          <template #header>Level 4</template>
          <p>Foreground</p>
          <template #footer>Level 4</template>
        </BaseCard>
      </div>
    `,
  }),
}

export const WithBorderAndShadow: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Cards with border and shadow variations.',
      },
    },
  },
  render: () => ({
    components: { BaseCard },
    template: `
      <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <BaseCard style="width: 250px;">
          <template #header>Standard Card</template>
          <p>Regular card without border or shadow.</p>
        </BaseCard>

        <BaseCard :border="true" style="width: 250px;">
          <template #header">With Border</template>
          <p>Card with explicit border enabled.</p>
        </BaseCard>

        <BaseCard :shadow="true" style="width: 250px;">
          <template #header>With Shadow</template>
          <p>Card with drop shadow effect.</p>
        </BaseCard>

        <BaseCard :border="true" :shadow="true" style="width: 250px;">
          <template #header>Border & Shadow</template>
          <p>Card with both border and shadow.</p>
        </BaseCard>
      </div>
    `,
  }),
}

export const DashboardCards: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Example dashboard cards showing real-world usage patterns.',
      },
    },
  },
  render: () => ({
    components: { BaseCard, BaseBadge, TrendingUp, DollarSign, User, Calendar },
    template: `
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1rem; max-width: 1200px;">
        <!-- Revenue Card -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <span>Total Revenue</span>
              <TrendingUp :size="20" style="color: var(--color-success);" />
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div style="font-size: 2rem; font-weight: bold; color: var(--color-success);">
              $24,567
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
              <BaseBadge purpose="success" size="xs">+12%</BaseBadge>
              <span style="font-size: 0.9rem; color: var(--color-text-soft);">vs last month</span>
            </div>
          </div>
        </BaseCard>

        <!-- Users Card -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <span>Active Users</span>
              <User :size="20" style="color: var(--color-primary);" />
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div style="font-size: 2rem; font-weight: bold; color: var(--color-primary);">
              1,234
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
              <BaseBadge purpose="info" size="xs">+5%</BaseBadge>
              <span style="font-size: 0.9rem; color: var(--color-text-soft);">vs last week</span>
            </div>
          </div>
        </BaseCard>

        <!-- Events Card -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <span>Upcoming Events</span>
              <Calendar :size="20" style="color: var(--color-warning);" />
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div style="font-size: 2rem; font-weight: bold; color: var(--color-warning);">
              18
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
              <BaseBadge purpose="warning" size="xs">3 today</BaseBadge>
              <span style="font-size: 0.9rem; color: var(--color-text-soft);">next 7 days</span>
            </div>
          </div>
        </BaseCard>
      </div>
    `,
  }),
}

export const ProductCards: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Product showcase cards with images, descriptions, and actions.',
      },
    },
  },
  render: () => ({
    components: { BaseCard, BaseButton, BaseBadge, Star, Heart },
    template: `
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; max-width: 1000px;">
        <!-- Product 1 -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <span>Wireless Headphones</span>
              <BaseBadge purpose="danger" size="xs">Sale</BaseBadge>
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">
              🎧
            </div>

            <div>
              <p style="margin: 0 0 0.5rem 0;">Premium wireless headphones with noise cancellation and 30-hour battery life.</p>
              <div style="display: flex; align-items: center; gap: 0.5rem; margin: 0.5rem 0;">
                <div style="display: flex; color: var(--color-warning);">
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" />
                </div>
                <span style="font-size: 0.9rem; color: var(--color-text-soft);">(4.2)</span>
              </div>
              <div style="font-size: 1.5rem; font-weight: bold; color: var(--color-primary);">
                $199.99
                <span style="font-size: 1rem; text-decoration: line-through; color: var(--color-text-soft); margin-left: 0.5rem;">$249.99</span>
              </div>
            </div>
          </div>

          <template #footer>
            <div style="display: flex; gap: 0.5rem;">
              <BaseButton purpose="primary" style="flex: 1;">Add to Cart</BaseButton>
              <BaseButton :icon="true" variant="outline" purpose="secondary">
                <Heart :size="16" />
              </BaseButton>
            </div>
          </template>
        </BaseCard>

        <!-- Product 2 -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <span>Smart Watch</span>
              <BaseBadge purpose="success" size="xs">New</BaseBadge>
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">
              ⌚
            </div>

            <div>
              <p style="margin: 0 0 0.5rem 0;">Advanced fitness tracking with heart rate monitor and GPS functionality.</p>
              <div style="display: flex; align-items: center; gap: 0.5rem; margin: 0.5rem 0;">
                <div style="display: flex; color: var(--color-warning);">
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                  <Star :size="16" fill="currentColor" />
                </div>
                <span style="font-size: 0.9rem; color: var(--color-text-soft);">(4.8)</span>
              </div>
              <div style="font-size: 1.5rem; font-weight: bold; color: var(--color-primary);">
                $299.99
              </div>
            </div>
          </div>

          <template #footer>
            <div style="display: flex; gap: 0.5rem;">
              <BaseButton purpose="primary" style="flex: 1;">Add to Cart</BaseButton>
              <BaseButton :icon="true" variant="outline" purpose="secondary">
                <Heart :size="16" />
              </BaseButton>
            </div>
          </template>
        </BaseCard>
      </div>
    `,
  }),
}

export const NotificationCards: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Notification and alert cards with different purposes.',
      },
    },
  },
  render: () => ({
    components: {
      BaseCard,
      BaseButton,
      BaseBadge,
      AlertCircle,
      Settings,
      Clock,
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 1rem; max-width: 500px;">
        <!-- Success Notification -->
        <BaseCard :border="true" :level="1">
          <div style="display: flex; align-items: start; gap: 1rem;">
            <div style="color: var(--color-success); padding: 0.5rem; background: var(--color-surface-success); border-radius: 50%;">
              ✓
            </div>
            <div style="flex: 1;">
              <h4 style="margin: 0 0 0.5rem 0; color: var(--color-success);">Payment Successful</h4>
              <p style="margin: 0; font-size: 0.9rem;">Your payment of $24.99 has been processed successfully.</p>
            </div>
            <BaseBadge purpose="success" size="xs">2m ago</BaseBadge>
          </div>
        </BaseCard>

        <!-- Warning Notification -->
        <BaseCard :border="true" :level="1">
          <div style="display: flex; align-items: start; gap: 1rem;">
            <div style="color: var(--color-warning); padding: 0.5rem; background: var(--color-surface-warning); border-radius: 50%;">
              <AlertCircle :size="16" />
            </div>
            <div style="flex: 1;">
              <h4 style="margin: 0 0 0.5rem 0; color: var(--color-warning);">Storage Almost Full</h4>
              <p style="margin: 0; font-size: 0.9rem;">You're using 89% of your available storage space.</p>
            </div>
            <BaseBadge purpose="warning" size="xs">1h ago</BaseBadge>
          </div>
          <template #footer>
            <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
              <BaseButton size="compact" purpose="warning">Upgrade Storage</BaseButton>
              <BaseButton size="compact" variant="ghost">Dismiss</BaseButton>
            </div>
          </template>
        </BaseCard>

        <!-- Info Notification -->
        <BaseCard :border="true" :level="1">
          <div style="display: flex; align-items: start; gap: 1rem;">
            <div style="color: var(--color-info); padding: 0.5rem; background: var(--color-surface-info); border-radius: 50%;">
              <Settings :size="16" />
            </div>
            <div style="flex: 1;">
              <h4 style="margin: 0 0 0.5rem 0; color: var(--color-info);">System Update Available</h4>
              <p style="margin: 0; font-size: 0.9rem;">A new system update is ready to install. Update now or schedule for later.</p>
            </div>
            <BaseBadge purpose="info" size="xs">3h ago</BaseBadge>
          </div>
          <template #footer>
            <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
              <BaseButton size="compact" purpose="info">Update Now</BaseButton>
              <BaseButton size="compact" variant="outline">Schedule</BaseButton>
            </div>
          </template>
        </BaseCard>
      </div>
    `,
  }),
}

export const ResponsiveExample: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates responsive behavior with container queries.',
      },
    },
  },
  render: () => ({
    components: { BaseCard, BaseButton, User, MapPin, Clock },
    template: `
      <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem; max-width: 800px;">
        <!-- Narrow Card (triggers responsive behavior) -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
              <User :size="20" />
              Profile (Narrow)
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div>
              <h4 style="margin: 0 0 0.5rem 0;">Sarah Johnson</h4>
              <div style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.9rem; color: var(--color-text-soft);">
                <MapPin :size="14" />
                San Francisco, CA
              </div>
            </div>
            <p style="margin: 0; font-size: 0.9rem;">UX Designer with a passion for creating intuitive user experiences.</p>
          </div>

          <template #footer>
            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
              <BaseButton size="compact" purpose="primary">View Profile</BaseButton>
              <BaseButton size="compact" variant="outline">Send Message</BaseButton>
            </div>
          </template>
        </BaseCard>

        <!-- Wide Card (normal layout) -->
        <BaseCard :shadow="true">
          <template #header>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
              <User :size="20" />
              Profile (Wide)
            </div>
          </template>

          <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div>
              <h4 style="margin: 0 0 0.5rem 0;">Sarah Johnson</h4>
              <div style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.9rem; color: var(--color-text-soft);">
                <MapPin :size="14" />
                San Francisco, CA
              </div>
            </div>
            <p style="margin: 0;">UX Designer with a passion for creating intuitive user experiences. Specializes in user research, prototyping, and design systems.</p>
          </div>

          <template #footer>
            <div style="display: flex; gap: 0.5rem;">
              <BaseButton size="compact" purpose="primary">View Profile</BaseButton>
              <BaseButton size="compact" variant="outline">Send Message</BaseButton>
            </div>
          </template>
        </BaseCard>
      </div>
    `,
  }),
}

export const AllCombinations: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive overview showing various combinations of props.',
      },
    },
  },
  render: () => ({
    components: { BaseCard, BaseButton },
    template: `
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; max-width: 1200px;">
        <!-- Default Combinations -->
        <BaseCard variant="default" :level="0">
          <template #header>Default Level 0</template>
          <p>Default variant with background level 0.</p>
        </BaseCard>

        <BaseCard variant="default" :level="2" :shadow="true">
          <template #header>Default + Shadow</template>
          <p>Default variant with shadow effect.</p>
        </BaseCard>

        <BaseCard variant="default" :border="true" :level="3">
          <template #header>Default + Border</template>
          <p>Default variant with explicit border.</p>
        </BaseCard>

        <!-- Outline Combinations -->
        <BaseCard variant="outline" :level="1">
          <template #header>Outline Level 1</template>
          <p>Outline variant with background level 1.</p>
        </BaseCard>

        <BaseCard variant="outline" :shadow="true" :level="2">
          <template #header">Outline + Shadow</template>
          <p>Outline variant with shadow effect.</p>
        </BaseCard>

        <BaseCard variant="outline" :border="true" :level="4">
          <template #header>Outline + Border</template>
          <p>Outline variant with explicit border.</p>
        </BaseCard>

        <!-- Complex Combinations -->
        <BaseCard variant="default" :border="true" :shadow="true" :level="1">
          <template #header>All Features</template>
          <p>Card with border, shadow, and custom level.</p>
          <template #footer>
            <BaseButton size="compact" purpose="primary">Action</BaseButton>
          </template>
        </BaseCard>

        <BaseCard variant="outline" :border="true" :shadow="true" :level="3">
          <template #header>Outline All Features</template>
          <p>Outline variant with all features enabled.</p>
          <template #footer>
            <BaseButton size="compact" variant="outline">Action</BaseButton>
          </template>
        </BaseCard>
      </div>
    `,
  }),
}
