<script setup lang="ts">
import { computed } from 'vue'

type Props = {
  variant?: 'default' | 'outline'
  border?: boolean
  shadow?: boolean
  level?: number
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  border: false,
  shadow: false,
  level: 2
})

const cardBg = computed(() => {
  const levelNumber = Number(props.level)

  if (typeof (levelNumber) !== 'number') return 'var(--color-bg-2)'

  return `var(--color-bg-${Math.max(0, Math.min(levelNumber, 4))})`
})
</script>

<template>
  <article class="layer card" :class="[
    `card--${props.variant}`,
    { 'card--outline': props.variant === 'outline' },
    { 'shadow': props.shadow },
    { 'border': props.border }
  ]">
    <header v-if="$slots.header" class="layer card-inner card__header">
      <h3>
        <slot name="header" />
      </h3>
    </header>

    <div class="layer card-inner card__content">
      <slot />
    </div>

    <footer v-if="$slots.footer" class="layer card-inner card__footer">
      <slot name="footer" />
    </footer>
  </article>
</template>

<style scoped>
.card {
  display: grid;
  height: 100%;
  padding: var(--space-m);
  border-radius: var(--radius-l);
  background-color: v-bind(cardBg);
  align-content: start;
}

.card-inner {
  display: grid;
  grid-template-rows: auto 1fr auto;
  transition: transform 0.2s ease;
}

.card--outline {
  border: 2px solid var(--color-text);
}

.border {
  border: 2px solid var(--color-border);
}

.card--outline.border {
  border: 2px solid var(--color-border);
  outline: 2px solid var(--color-text);
}

/* Header styles */
.card__header {
  margin: 0;

  &:has(+ *) {
    margin-block-end: .5rem;
  }

  h3 {
    color: var(--color-text-soft);
  }
}

/* Content styles */
.card__content {
  display: grid;
  gap: .5rem;

  &:has(+ *) {
    margin-block-end: .5rem;
  }
}

/* Footer styles */
.card__footer {
  margin-top: auto;
}

/* Container query based responsive adjustments */
@container card (max-width: 400px) {
  .card__header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card__footer {
    flex-direction: column;
  }
}
</style>
