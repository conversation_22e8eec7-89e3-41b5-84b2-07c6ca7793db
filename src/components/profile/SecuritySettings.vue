<script setup lang="ts">
import { ref, computed } from 'vue'
import { useProfile } from '@/composables/user/useProfile'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { LockIcon, MailIcon, ShieldIcon } from 'lucide-vue-next'
import type { PasswordUpdateData } from '@/types/user'

const { auth } = useFirebase()
const { updateUserPassword, resendVerificationEmail } = useProfile()

const isChangingPassword = ref(false)
const passwordData = ref<PasswordUpdateData>({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isEmailVerified = computed(() => auth.currentUser?.emailVerified ?? false)

const passwordError = ref<string | null>(null)
const passwordSuccess = ref<string | null>(null)

const passwordStrength = computed(() => {
  const password = passwordData.value.newPassword
  if (!password) return 0

  let strength = 0
  if (password.length >= 8) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^A-Za-z0-9]/.test(password)) strength++

  return strength
})

const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1:
      return 'Very Weak'
    case 2:
      return 'Weak'
    case 3:
      return 'Medium'
    case 4:
      return 'Strong'
    case 5:
      return 'Very Strong'
    default:
      return ''
  }
})

const passwordStrengthColor = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1:
      return 'var(--color-danger)'
    case 2:
      return 'var(--color-warning)'
    case 3:
      return 'var(--color-info)'
    case 4:
    case 5:
      return 'var(--color-success)'
    default:
      return 'var(--color-border)'
  }
})

async function handlePasswordChange() {
  passwordError.value = null
  passwordSuccess.value = null

  // Validate passwords
  if (passwordData.value.newPassword !== passwordData.value.confirmPassword) {
    passwordError.value = 'New passwords do not match'
    return
  }

  if (passwordStrength.value < 3) {
    passwordError.value = 'Password is too weak. Please choose a stronger password.'
    return
  }

  const success = await updateUserPassword(passwordData.value)

  if (success) {
    passwordSuccess.value = 'Password updated successfully'
    passwordData.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    isChangingPassword.value = false
  }
}

async function handleResendVerification() {
  await resendVerificationEmail()
}
</script>

<template>
  <BaseSection title="Security Settings">
    <div class="security-settings">
      <!-- Email Verification Status -->
      <BaseCard>
        <div class="setting-item">
          <div class="setting-header">
            <MailIcon :size="20" />
            <h3>Email Verification</h3>
          </div>

          <div class="setting-content">
            <p v-if="isEmailVerified" class="verified-status">
              Your email is verified
            </p>
            <template v-else>
              <p class="unverified-status">
                Your email is not verified
              </p>
              <BaseButton type="secondary" @click="handleResendVerification">
                Resend Verification Email
              </BaseButton>
            </template>
          </div>
        </div>
      </BaseCard>

      <!-- Password Change -->
      <BaseCard>
        <div class="setting-item">
          <div class="setting-header">
            <LockIcon :size="20" />
            <h3>Password</h3>
          </div>

          <div class="setting-content">
            <template v-if="!isChangingPassword">
              <p>Change your password to keep your account secure</p>
              <BaseButton type="secondary" @click="isChangingPassword = true">
                Change Password
              </BaseButton>
            </template>

            <form v-else @submit.prevent="handlePasswordChange" class="password-form">
              <BaseInput v-model="passwordData.currentPassword" type="password" label="Current Password" required />

              <BaseInput v-model="passwordData.newPassword" type="password" label="New Password" required />

              <div class="password-strength">
                <div class="strength-bar" :style="{
                  '--strength': `${(passwordStrength / 5) * 100}%`,
                  '--color': passwordStrengthColor
                }" />
                <span class="strength-text" :style="{ color: passwordStrengthColor }">
                  {{ passwordStrengthText }}
                </span>
              </div>

              <BaseInput v-model="passwordData.confirmPassword" type="password" label="Confirm New Password" required />

              <p v-if="passwordError" class="error-message" role="alert">
                {{ passwordError }}
              </p>

              <p v-if="passwordSuccess" class="success-message" role="status">
                {{ passwordSuccess }}
              </p>

              <div class="form-actions">
                <BaseButton type="secondary" @click="isChangingPassword = false">
                  Cancel
                </BaseButton>
                <BaseButton type="primary" native-type="submit">
                  Update Password
                </BaseButton>
              </div>
            </form>
          </div>
        </div>
      </BaseCard>

      <!-- Two-Factor Authentication -->
      <BaseCard>
        <div class="setting-item">
          <div class="setting-header">
            <ShieldIcon :size="20" />
            <h3>Two-Factor Authentication</h3>
          </div>

          <div class="setting-content">
            <p>Coming soon! Add an extra layer of security to your account.</p>
          </div>
        </div>
      </BaseCard>
    </div>
  </BaseSection>
</template>

<style scoped>
.security-settings {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.setting-header {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--color-heading);
}

.setting-header h3 {
  margin: 0;
  font-size: var(--step-1);
  font-weight: 600;
}

.setting-content {
  color: var(--color-text);
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  max-width: 400px;
}

.form-actions {
  display: flex;
  gap: var(--space-s);
  justify-content: flex-end;
  margin-top: var(--space-m);
}

.password-strength {
  display: flex;
  align-items: center;
  gap: var(--space-s);
  margin-top: var(--space-2xs);
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: var(--color-border);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.strength-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: var(--strength);
  background: var(--color);
  transition: all 0.3s ease;
}

.strength-text {
  font-size: var(--step--1);
  font-weight: 500;
}

.verified-status {
  color: var(--color-success);
  display: flex;
  align-items: center;
  gap: var(--space-2xs);
}

.unverified-status {
  color: var(--color-warning);
  margin-bottom: var(--space-s);
}

.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
}

.success-message {
  color: var(--color-success);
  font-size: var(--step--1);
}
</style>
