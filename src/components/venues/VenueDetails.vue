<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { collection, query, where, getDocs } from 'firebase/firestore'
import { Phone, Mail, MapPin } from 'lucide-vue-next'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { useRoute } from 'vue-router'
import { Venue } from '@/models/Venue'
import BaseSection from '@/components/base/BaseSection.vue'

const route = useRoute()
const { db } = useFirebase()
const venue = ref<Venue | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

onMounted(async () => {
  try {
    const venueName = decodeURIComponent(Array.isArray(route.params.name) ? route.params.name[0] : route.params.name)

    if (!venueName) {
      throw new Error('Venue name is missing')
    }

    const venuesRef = collection(db, 'venues')
    const q = query(venuesRef, where('name', '==', venueName))
    const snapshot = await getDocs(q)

    if (!snapshot.empty) {
      const venueData = {
        id: snapshot.docs[0].id,
        ...snapshot.docs[0].data()
      }
      venue.value = Venue.fromPlainObject(venueData)
    }
  } catch (err) {
    console.error('Error fetching venue:', err)
    error.value = 'Failed to load venue'
  } finally {
    isLoading.value = false
  }
})

async function handleNotesUpdate(newNotes: Record<string, string>) {
  try {
    isLoading.value = true
    await venue.value?.update({ notes: newNotes })
    if (venue.value) {
      venue.value.notes = newNotes
    }
  } catch (err) {
    console.error('Error updating notes:', err)
    error.value = 'Failed to update notes'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <BaseSection>
    <div v-if="isLoading" class="loading">Loading...</div>
    <div v-else-if="error" class="error">{{ error }}</div>

    <div v-else-if="venue" class="venue-card">
      <header class="venue-header">
        <h2>{{ venue.name }}</h2>
      </header>

      <div class="contact-grid">
        <div v-if="venue.phone" class="contact-item">
          <Phone class="icon" />

          {{ venue.phone }}
        </div>

        <div v-if="venue.email" class="contact-item">
          <Mail class="icon" />
          <a :href="'mailto:' + venue.email">{{ venue.email }}</a>
        </div>

        <div v-if="venue.address" class="contact-item">
          <MapPin class="icon" />
          <div>{{ venue.getFormattedAddress() }}</div>
        </div>
      </div>

      <!-- Notes Section -->
      <!--
      <section class="notes-section">
        <h3>Notes</h3>
        <NotesEditor v-model:notes="venue.notes" :is-disabled="isLoading" @update:notes="handleNotesUpdate" />
      </section>
      -->
    </div>

    <div v-else class="not-found">
      Venue not found
    </div>
  </BaseSection>
</template>

<style scoped>
.venue-card {
  background: var(--color-background);
  border-radius: var(--br-step-0);
  padding: var(--pad-step--2);
  box-shadow: var(--shadow-step-1);
}

.venue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-step--2);
  padding-bottom: var(--pad-step--3);
  border-bottom: 1px solid var(--color-border);
}

.venue-header h2 {
  margin: 0;
  font-size: var(--step--1);
}

.venue-type {
  font-size: var(--step--4);
  padding: var(--pad-step--3) var(--pad-step--2);
  background: var(--color-primary-soft);
  color: var(--color-primary);
  border-radius: var(--br-step--2);
}

.contact-grid {
  display: grid;
  gap: var(--space-step--3);
  margin-bottom: var(--space-step--2);
}

.contact-item {
  display: flex;
  gap: var(--space-step--3);
  align-items: flex-start;
}

.icon {
  width: 0.9em;
  height: 0.9em;
  flex-shrink: 0;
}

.notes-section {
  display: grid;
  gap: 1rem;
}

.timeline {
  display: grid;
  gap: var(--space-step--3);
}

.timeline-event {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--space-step--3);
  padding: var(--pad-step--3);
  background: var(--color-background-soft);
  border-radius: var(--br-step--2);
}

.event-date {
  display: flex;
  align-items: center;
  gap: var(--space-step--4);
  color: var(--color-text-soft);
  font-size: var(--step--4);
}

h3 {
  font-size: var(--step--2);
  margin: var(--space-step--3) 0;
}

.loading,
.error,
.not-found {
  text-align: center;
  padding: 2rem;
  color: var(--color-text-muted);
}

.error {
  color: var(--color-error);
}

@media (max-width: 768px) {
  .venue-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-step--3);
  }

  .timeline-event {
    grid-template-columns: 1fr;
  }
}
</style>
