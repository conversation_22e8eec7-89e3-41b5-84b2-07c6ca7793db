<script setup lang="ts">
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { Venue } from '@/models/Venue'
import { MapPin, Phone, Mail, Globe, Eye, Edit } from 'lucide-vue-next'

type Props = {
  venue: Venue
}

const props = defineProps<Props>()

const computedAddress = computed(() => props.venue.getFormattedAddress())
</script>

<template>
  <BaseCard :level="1">
    <template #header>
      <div class="venue-card__header">
        <RouterLink :to="{ name: 'venues.show', params: { id: venue.id } }">
          {{ venue.getName() }}
        </RouterLink>
      </div>
    </template>

    <div class="venue-card__content">
      <div class="venue-card__address">
        <MapPin class="icon" />
        <span>{{ computedAddress }}</span>
      </div>
      <a v-if="venue.phone" :href="`tel:${venue.phone}`" class="venue-card__link">
        <Phone class="icon" />
        <span>{{ venue.phone }}</span>
      </a>
      <a v-if="venue.email" :href="`mailto:${venue.email}`" class="venue-card__link">
        <Mail class="icon" />
        <span>Email</span>
      </a>
      <a v-if="venue.website" :href="venue.website" target="_blank" rel="noopener noreferrer" class="venue-card__link">
        <Globe class="icon" />
        <span>Website</span>
      </a>
    </div>

    <template #footer>
      <div class="venue-card__actions">
        <RouterLink :to="{ name: 'venues.show', params: { id: venue.id } }" class="base-button level-0">
          <Eye class="icon" />
        </RouterLink>
        <RouterLink :to="{ name: 'venues.edit', params: { id: venue.id } }" class="base-button level-0">
          <Edit class="icon" />
        </RouterLink>
      </div>
    </template>
  </BaseCard>
</template>

<style scoped>
.venue-card {
  transition: all 0.2s ease;
  display: grid;
  grid-template-rows: auto auto auto;
}

.base-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--step--1);
  background-color: var(--color-bg-2);
  padding: var(--space-3xs);
}

.venue-card__header {
  color: var(--color-heading);
  font-size: var(--step-0);
  text-decoration: underline;
  text-underline-offset: 0.25rem;
  text-decoration-color: var(--color-brand);
}

.venue-card__address {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--step--1);
}

.venue-card__content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem 1.6rem;
  font-size: var(--step--1);

  a {
    padding: 0;
  }
}

.venue-card__link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-light);
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.venue-card__link:hover {
  background: var(--color-background-mute);
  color: var(--color-text);
}

.venue-card__actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.venue-card__address .icon,
.venue-card__link .icon,
.action-button .icon {
  color: inherit;
}
</style>
