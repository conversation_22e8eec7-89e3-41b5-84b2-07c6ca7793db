<script setup lang="ts">
import { CheckIcon, Edit, Eye, Pencil } from 'lucide-vue-next'
import { User } from '@/models/User'
import { useAuth } from '@/composables/firebase/useAuth'
import { useUsers } from '@/composables/user/useUsers'

const { isAdmin } = useAuth()
const { isCurrentUser } = useUsers()

defineProps<{
  user: User
}>()

function formatRoleName(roleName: string | undefined): string {
  if (!roleName) return ''

  switch (roleName.toLowerCase()) {
    case 'artist':
      return 'Artist'
    case 'bandleader':
      return 'Band Leader'
    case 'admin':
      return 'Administrator'
    case 'user':
      return 'User'
    default:
      return roleName.charAt(0).toUpperCase() + roleName.slice(1)
  }
}
</script>

<template>
  <BaseCard class="user-card" :class="{ 'current-user': isCurrentUser(user) }">
    <div class="user-content">
      <div class="user-info">
        <div class="user-name">
          <BaseBadge v-if="isCurrentUser(user)" variant="solid" purpose="primary" size="xs" aria-label="This is you"
            title="This is you">
            You
          </BaseBadge>
          <BaseBadge v-if="user.isSubscribed" variant="solid" purpose="success" size="xs"
            aria-label="Verified Subscriber" title="Verified Subscriber" shape="octostar">
            <CheckIcon class="icon" stroke-width="4" />
          </BaseBadge>
          <div class="name-text">{{ user.fullName }} <span class="date-of-signup">{{ user.createdAt ? `since
              ${user.createdAt.toDate().getFullYear()}` : ''
              }}</span></div>
        </div>
        <div class="user-email">
          {{ user.email }}
        </div>
        <div class="user-roles">
          <BaseBadge v-for="(role, index) in user.roles?.filter(r => r?.type)" :key="role.type || index" variant="ghost"
            purpose="secondary" size="xs">
            {{ formatRoleName(role.type) }}
          </BaseBadge>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <div class="user-actions">
        <RouterLink v-if="isCurrentUser(user) || isAdmin" :to="{ name: 'edit-user', params: { id: user.id } }"
          title="Edit Profile" aria-label="Edit Profile" role="button" :class="{ 'admin-only': !isCurrentUser(user) }">
          <Edit class="icon" />
        </RouterLink>
        <RouterLink :to="{ name: 'user-details', params: { id: user.id } }" title="View Details"
          aria-label="View Details" role="button">
          <Eye class="icon" />
        </RouterLink>
      </div>
    </div>
  </BaseCard>
</template>

<style scoped>
.user-card {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.user-content {
  flex: 1;
}

.card-footer {
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-s);
  margin-top: auto;
}

.user-actions {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: var(--space-xs);

  a {
    padding: .3em;
    background-color: var(--color-background-mute);
    border: 1px solid var(--color-border);

    &.admin-only {
      color: var(--color-brand);
    }

    &:hover {
      background-color: var(--color-background-soft);
      color: var(--fg);
    }
  }
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--color-heading);
  display: flex;
  align-items: center;
  margin-bottom: var(--space-xs);
  gap: var(--space-2xs);
}

.name-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.date-of-signup {
  font-size: var(--step--2);
  color: var(--color-text-soft);
}

.user-email {
  color: var(--color-text-muted);
  font-size: var(--step--1);
  margin-bottom: var(--space-xs);
}

.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2xs);
}

.role-badge,
.verified-badge-wrapper,
.verified-badge,
.me-badge {
  display: none;
}

.auth-details {
  font-size: var(--step--1);
  color: var(--color-text-muted);
  margin-top: var(--space-2xs);
  display: flex;
  flex-direction: column;
  gap: var(--space-3xs);
}
</style>
