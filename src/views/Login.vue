<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { signInWithEmailAndPassword, type UserCredential, type Auth } from 'firebase/auth'
import { useFirebase } from '@/composables/firebase/useFirebase'

const { auth } = useFirebase()
const router = useRouter()
const email = ref<string>('')
const password = ref<string>('')
const error = ref<string>('')
const isLoading = ref<boolean>(false)

type CustomClaims = {
  admin: boolean
  [key: string]: any
}

async function handleLogin(): Promise<void> {
  try {
    isLoading.value = true
    error.value = ''

    const userCredential: UserCredential = await signInWithEmailAndPassword(
      auth as Auth,
      email.value,
      password.value
    )
    const idTokenResult = await userCredential.user.getIdTokenResult()
    const claims = idTokenResult.claims as CustomClaims

    if (!claims.admin) {
      await auth.signOut()
      error.value = 'Access denied. Admin privileges required.'
      return
    }

    router.push('/')
  } catch (e) {
    error.value = 'Invalid login credentials'
    console.error('Login error:', e)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <form @submit.prevent="handleLogin" class="login-form">
    <header class="form-header">
      <h1>Admin Login</h1>
    </header>

    <div class="form-section">
      <div class="form-group">
        <label for="email">Email</label>
        <input id="email" type="email" v-model="email" required :disabled="isLoading" autocomplete="email" />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input id="password" type="password" v-model="password" required :disabled="isLoading"
          autocomplete="current-password" />
      </div>
    </div>

    <div class="form-actions">
      <BaseButton type="submit" purpose="primary" :disabled="isLoading">
        {{ isLoading ? 'Logging in...' : 'Login' }}
      </BaseButton>
    </div>

    <p v-if="error" class="error-message">{{ error }}</p>
  </form>
</template>

<style scoped>
.login-form {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-header {
  text-align: center;
}

.form-header h1 {
  color: var(--color-heading);
  font-size: var(--step-2);
  margin: 0;
}

.form-section {
  background: var(--color-background-soft);
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: var(--step--1);
  color: var(--color-text);
}

.form-group input {
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-accent);
}

.form-group input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  justify-content: center;
}

.button {
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  border: none;
  font-size: var(--step--1);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-in);
}

.button--primary {
  background: var(--color-accent);
  color: var(--color-background);
}

.button--primary:hover:not(:disabled) {
  filter: brightness(1.1);
}

.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.error-message {
  text-align: center;
  color: var(--color-text-danger);
  font-size: var(--step--1);
  margin: 0;
}
</style>
