<script setup lang="ts">
import { computed, onMounted } from 'vue'
import CalendarHeader from '@/components/calendar/CalendarHeader.vue'
import CalendarDay from '@/components/calendar/CalendarDay.vue'
import { useEvents } from '@/composables/events/useEvents'
import { useCalendar, type Day } from '@/composables/calendar/useCalendar'
import { Event } from '@/models/Event'
import type { Artist } from '@/models/Artist'

const CALENDAR_VISITED_KEY = 'calendar-visited'
const EXPIRE_AFTER_HOURS = 24


// Subscribe to events with default filters
const { subscribeToEvents, events, isLoading, areDetailsLoading } = useEvents({
  sortDescending: false,
  includePaid: true
})

// Calendar setup
const {
  selectedDates,
  hasOlderEvents,
  visibleMonthDate,
  weeks,
  allMonths,
  toggleCalendarSize,
  moveBackward,
  moveForward,
  resetToToday,
  navigateToMonth,
  selectDate,
} = useCalendar()

// Subscribe to events when component mounts
onMounted(async () => {
  const today = new Date()
  const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 6, today.getDate())
  const sixMonthsAhead = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate())

  await subscribeToEvents({
    fromDate: sixMonthsAgo,
    toDate: sixMonthsAhead,
    includePaid: true
  })

  // Check if this is the first visit or if the previous visit has expired
  const visitedData = localStorage.getItem(CALENDAR_VISITED_KEY)

  if (visitedData) {
    try {
      const { timestamp } = JSON.parse(visitedData)
      const expiryTime = timestamp + (EXPIRE_AFTER_HOURS * 60 * 60 * 1000)

      if (Date.now() > expiryTime) {
        localStorage.removeItem(CALENDAR_VISITED_KEY)
      }
    } catch (e) {
      console.error('Error parsing calendar visit data:', e)
    }
  }
})

// Compute weeks with events, considering loading states
const weeksWithEvents = computed(() => {
  if (isLoading.value || areDetailsLoading.value) {
    // Return empty gigDetails while loading
    return weeks.value.map(week =>
      week.map(day => ({
        ...day,
        gigDetails: []
      }))
    )
  }

  return weeks.value.map(week =>
    week.map(day => ({
      ...day,
      gigDetails: events.value
        .filter(event => {
          const eventDate = event.when.toDate()
          return eventDate.toDateString() === day.date.toDateString()
        })
        // Make sure we're working with Event instances
        .map(event => {
          if (!(event instanceof Event)) {
            // If it's not an Event instance, create one
            return new Event(event as Event & { id: string })
          }
          return event
        })
    }))
  )
})

// Event handlers
function handleDaySelect(day: Day, event?: MouseEvent): void {
  selectDate(day.date, event)

  // Set visited flag when user manually selects a date
  if (selectedDates.value.length > 0) {
    localStorage.setItem(CALENDAR_VISITED_KEY, JSON.stringify({
      timestamp: Date.now()
    }))
  } else {
    // Clear visited flag when user deselects all dates
    localStorage.removeItem(CALENDAR_VISITED_KEY)
  }
}

function handleUnavailableClick(day: Day, artist: Artist): void {
  console.log('Unavailable clicked:', day, artist)
}

const formattedDates = computed(() => {
  if (selectedDates.value.length === 1) {
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    }).format(selectedDates.value[0])
  }

  return `${selectedDates.value.length} dates selected`
})
</script>

<template>
  <BaseSection title="Calendar" minimal>
    <div class="calendar">
      <CalendarHeader :current-time="visibleMonthDate" :all-months="allMonths" :hasOlderEvents="hasOlderEvents"
        @navigate-month="navigateToMonth" @toggle-size="toggleCalendarSize" @reset-today="resetToToday"
        @move-backward="moveBackward" @move-forward="moveForward" />

      <div class="calendar__grid">
        <div v-if="isLoading || areDetailsLoading" class="calendar__loading">
          Loading events...
        </div>

        <template v-else>
          <div class="calendar__weekdays">
            <div v-for="day in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" :key="day" class="calendar__weekday">
              {{ day }}
            </div>
          </div>

          <div class="calendar__weeks">
            <div v-for="(week, weekIndex) in weeksWithEvents" :key="weekIndex" class="calendar__week">
              <CalendarDay v-for="(day, dayIndex) in week" :key="`${weekIndex}-${dayIndex}`" :day
                :initial-day="dayIndex === 0" :is-past="false" :selected-dates="selectedDates" @select="handleDaySelect"
                @unavailable-click="handleUnavailableClick" />
            </div>
          </div>
        </template>
      </div>

      <div class="calendar__date-display">
        {{ formattedDates }}
      </div>
    </div>
  </BaseSection>
</template>

<style scoped>
.calendar-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.nav-button,
.toggle-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  cursor: pointer;
  transition: all 0.02s ease;
}

.nav-button:hover,
.toggle-button:hover {
  background: var(--color-background-alt);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-button {
  margin-left: auto;
}

.calendar {
  width: 100%;
  background: var(--color-background);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.calendar__grid {
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  overflow: hidden;
}

.calendar__weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: var(--color-background-alt);
  border-bottom: 1px solid var(--color-border);
}

.calendar__weekday {
  padding: 0.5rem;
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
}

.calendar__weeks {
  display: flex;
  flex-direction: column;
}

.calendar__week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid var(--color-border);
}

.calendar__week:last-child {
  border-bottom: none;
}

.calendar-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.calendar-modal__content {
  background: var(--color-background);
  padding: 2rem;
  border-radius: 0.5rem;
  position: relative;
  width: 90%;
  max-width: 1200px;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: var(--color-background-alt);
}

.calendar__date-display {
  text-align: center;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--color-text-muted);
}

.calendar__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--color-text-muted);
}
</style>
