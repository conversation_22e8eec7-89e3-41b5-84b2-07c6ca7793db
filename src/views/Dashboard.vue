<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUsers } from '@/composables/user/useUsers.ts'
import { useFirebase } from '@/composables/firebase/useFirebase.ts'
import { User } from '@/models/User'

import UpcomingGigs from '@/components/UpcomingGigs.vue'
import UpcomingRevenue from '@/components/UpcomingRevenue.vue'
import UnpaidGigs from '@/components/UnpaidGigs.vue'
import BaseCard from '@/components/base/BaseCard.vue'

// Get the current user using useUsers composable
const { getCurrentUserData } = useUsers()
const currentUser = ref<User | null>(null)

onMounted(async () => {
  currentUser.value = await getCurrentUserData()
})

// Get the current user
const { currentUserFirstName } = useFirebase()

// Add state for date range with default fromDate as today
const today = new Date()
today.setHours(0, 0, 0, 0) // Set to start of day
const revenueFromDate = ref<Date>(today)
const revenueToDate = ref<Date | null>(null)

// These values would normally come from your API/composables.
const notifications = ref([
  { id: 1, message: 'Venue X confirmed your booking.' },
  { id: 2, message: 'Invoice #123 is pending.' }
])

// For the financial trends chart, you could hook in Chart.js or ApexCharts later.
</script>

<template>
  <h1>Dashboard</h1>

  <h2 class="welcome">Welcome, {{ currentUser?.firstName || currentUserFirstName || 'Guest' }}!</h2>

  <div class="dashboard-grid">
    <BaseSection title="Gig Management" minimal>
      <div class="gig-management-grid">
        <BaseCard border :level="1">
          <template #header>Gigs</template>
          <UpcomingGigs />
        </BaseCard>
        <BaseCard border :level="1">
          <template #header>Revenue</template>
          <UpcomingRevenue v-model:fromDate="revenueFromDate" v-model:toDate="revenueToDate" />
        </BaseCard>
        <BaseCard border :level="1">
          <template #header>Unpaid Gigs</template>
          <UnpaidGigs />
        </BaseCard>
      </div>
    </BaseSection>
  </div>
</template>

<style scoped>
h1,
h2 {
  margin-bottom: 1rem;
}

.welcome {
  color: var(--color-brand);
  font-size: var(--step-2);
}

/* Grid for the quick stats cards */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.gig-management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1rem;
}

/* Chart container */
.chart-container {
  background: var(--color-background-soft);
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

/* Mini calendar & notifications */
.dashboard-calendar,
.dashboard-notifications {
  background: var(--color-background-soft);
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.dashboard-notifications ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.user-info-debug {
  background: var(--color-background-soft);
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  font-family: monospace;
  white-space: pre-wrap;
  overflow-x: auto;
}

.user-info-debug pre {
  margin: 0;
  font-size: var(--step--1);
}
</style>
