<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { collection, getDocs } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { Eye } from 'lucide-vue-next'
import ActBadgeLogo from '@/components/acts/ActBadgeLogo.vue'
import { useActs } from '@/composables/acts/useActs'
import type { Song } from '@/types/song'

const { db } = useFirebase()
const songs = ref<Song[]>([])
const loading = ref(true)
const selectedActs = ref<Set<string>>(new Set())

const { acts, subscribeToActs, cleanup } = useActs()

// Fetch songs
async function fetchSongs(): Promise<void> {
  try {
    loading.value = true
    const snapshot = await getDocs(collection(db, 'repertoire'))
    songs.value = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Song[]
  } catch (error) {
    console.error('Error fetching songs:', error)
  } finally {
    loading.value = false
  }
}

// Filter songs based on selected acts
const filteredSongs = computed(() => {
  if (selectedActs.value.size === 0) return songs.value

  return songs.value.filter(song =>
    song.acts.some(act => selectedActs.value.has(act))
  )
})

// Toggle act selection
function toggleAct(actId: string): void {
  if (selectedActs.value.has(actId)) {
    selectedActs.value.delete(actId)
  } else {
    selectedActs.value.add(actId)
  }
  selectedActs.value = new Set(selectedActs.value)
}

// Sort songs by title
const sortedSongs = computed(() => {
  return [...filteredSongs.value].sort((a, b) => a.title.localeCompare(b.title))
})

// Helper to get act by ID from the acts array
function getActById(actId: string) {
  return acts.value.find(act => act.id === actId)
}

function getValidActs(songActs: string[]): string[] {
  return songActs.filter(actId => acts.value.some(act => act.id === actId))
}

onMounted(async () => {
  subscribeToActs()
  await fetchSongs()
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <BaseSection minimal>
    <template #header>
      <h2>Songs</h2>
    </template>

    <!-- Act filters using badge logos -->
    <div class="act-filters">
      <BaseButton v-for="act in acts" :key="act.id" class="act-filter"
        :class="{ 'act-filter--selected': selectedActs.has(act.id) }" @click="toggleAct(act.id)" :title="act.name"
        :shadow="false">
        <ActBadgeLogo v-if="act.getLogoUrl('badge')" :actId="act.id" class="act-badge-logo" size="1.5em" />
        <span v-else class="act-name">{{ act.name }}</span>
      </BaseButton>
    </div>

    <!-- Songs list -->
    <div v-if="loading" class="loading">Loading songs...</div>

    <div v-else class="songs-list">
      <div v-for="song in sortedSongs" :key="song.id" class="song-item">
        <div class="song-title">{{ song.title }}</div>
        <div class="song-meta">
          <span v-if="song.key" class="song-key">{{ song.key }}</span>
          <span v-if="song.tempo" class="song-tempo">{{ song.tempo }} BPM</span>
        </div>
        <div class="song-acts">
          <template v-for="actId in getValidActs(song.acts)" :key="actId">
            <ActBadgeLogo v-if="getActById(actId)?.getLogoUrl('badge')" :actId="getActById(actId)!.id"
              class="act-badge" />

            <span v-else class="act-name-small">{{ getActById(actId)!.name }}</span>
          </template>
        </div>
        <RouterLink class="view-song" :to="`/songs/${song.id}`">
          <Eye class="icon" />
        </RouterLink>
      </div>
    </div>
  </BaseSection>
</template>

<style scoped>
.repertoire-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.act-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.act-filter {
  padding: 0.25rem;
  border: 2px solid var(--color-border);
  border-radius: 50%;
  background: var(--color-background);
  cursor: pointer;
  transition: all var(--transition-in);
  position: relative;

  &:hover,
  &:focus-visible {
    border-color: var(--color-warning);
    transform: translateY(-2px);
  }

  &.act-filter--selected {
    background-color: var(--color-accent-dark-alpha);

    &::after {
      content: '';
      position: absolute;
      inset: -4px;
      border: 2px solid var(--color-accent);
      border-radius: 50%;
      opacity: 0.5;
    }

    &:hover,
    &:focus-visible {
      transform: translateY(-2px);
    }
  }
}

.act-badge {
  width: 1em;
  height: auto;
  transition: transform var(--transition-in);

  &:hover,
  &:focus-visible {
    transform: scale(1.1);
  }
}

.act-name {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.act-name-small {
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

.act-filter-badge {
  display: block;
  transition: filter var(--transition-in);
}

.act-filter:hover .act-filter-badge .act-filter:focus-visible .act-filter-badge {
  filter: brightness(1.1);
}

.songs-list {
  display: grid;
  gap: 0.1rem;
  grid-template-columns: auto 1fr auto auto;
  border-radius: var(--radius-s);
  overflow: hidden;
}

.song-item {
  display: grid;
  grid-template-columns: subgrid;
  grid-column: 1 / -1;
  line-height: 1;
  align-items: center;
  gap: 1rem;
  padding: .25ch 1ch;
  background-color: var(--color-bg-1);

  &:nth-child(even) {
    background: var(--color-bg-2);
  }

  .song-title {
    font-weight: 500;
  }

  .song-meta {
    display: flex;
    gap: 1rem;
    color: var(--color-text-soft);
    font-size: 0.875rem;
  }

  .song-acts {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .view-song {
    display: grid;
    color: var(--color-text-muted);
    transition: color var(--transition-in);

    &:hover {
      color: var(--color-text);
      transition: color var(--transition-out);
    }
  }
}

.loading {
  text-align: center;
  padding: 2rem;
  color: var(--color-text-muted);
}

@media (max-width: 768px) {
  .songs-list {
    grid-template-columns: auto 1fr;
  }

  .song-item {
    gap: 0;
    padding-block: .25em;
  }
}
</style>
