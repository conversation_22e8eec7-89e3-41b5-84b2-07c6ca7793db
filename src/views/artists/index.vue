<script setup lang="ts">
import { onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useArtists } from '@/composables/artists/useArtists'
import ArtistCard from '@/components/artists/ArtistCard.vue'
import { Plus } from 'lucide-vue-next'
import { Artist } from '@/models/Artist'

const router = useRouter()
const { artists, isLoadingArtists, cleanup } = useArtists()

const sortedArtists = computed<Artist[]>(() => {
  return [...artists.value].sort((a, b) =>
    a.stageName.localeCompare(b.stageName)
  )
})

const navigateToCreateArtist = () => {
  router.push('/artists/create')
}

// Cleanup subscription when component is unmounted
onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <header class="artists-header">
    <div class="section-header">
      <h1>Artists</h1>
    </div>
    <BaseButton size="compact" @click="navigateToCreateArtist" title="Add artist">
      <Plus class="icon" /> New Artist
    </BaseButton>
  </header>

  <p v-if="isLoadingArtists" class="loading">Loading...</p>
  <div v-else-if="!artists.length" class="empty-state">
    <p>No artists found</p>
    <BaseButton class="action-button" @click="navigateToCreateArtist">
      Add your first artist
    </BaseButton>
  </div>
  <ul v-else class="artists">
    <ArtistCard v-for="artist in sortedArtists" :key="artist.id" :artist="artist" />
  </ul>
</template>

<style scoped>
.artists {
  display: grid;
  gap: .6em;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}



.artists-header {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.artists-controls {
  display: flex;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-background-soft);
  border-radius: 1rem;
  border: 1px solid var(--color-border);
}

.empty-state p {
  color: var(--color-text-muted);
  margin-bottom: 1rem;
}

.add-circle-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  background-color: var(--color-accent);
  color: var(--color-background);
  font-size: var(--step-1);
  line-height: 1;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-circle-button:hover {
  filter: brightness(1.1);
  transform: scale(1.1);
}

/* Show button on section hover */
.section-header:hover .add-circle-button {
  opacity: 1;
  visibility: visible;
}

/* Always show button if hover is not supported */
@media (hover: none) {
  .add-circle-button {
    opacity: 1;
    visibility: visible;
  }
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

main {
  margin-top: 0;
}
</style>
