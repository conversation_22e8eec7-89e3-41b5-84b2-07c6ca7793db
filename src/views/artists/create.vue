<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { collection, addDoc, doc, updateDoc, Timestamp } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { useArtists } from '@/composables/artists/useArtists'
import { Artist } from '@/models/Artist'

const router = useRouter()
const route = useRoute()
const { db } = useFirebase()
const { getArtist } = useArtists()

// Need to check if we are editing an existing artist
const isEditing = computed(() => !!route.params.id)
const artistId = computed(() => route.params.id as string)

// State for artist data when editing
const artist = ref<Artist | null>(null)
const isLoadingArtist = ref(false)
const loadError = ref<string | null>(null)

const isSubmitting = ref(false)
const submitError = ref<string | null>(null)

const form = ref({
  firstName: '',
  lastName: '',
  stageName: '',
  instruments: '', // Changed to string for the input field
  photoId: ''
})

const photoPreview = ref<string | null>(null)
const photoFile = ref<File | null>(null)

// Computed property to get the preview URL - either from file or Cloudinary publicId
const previewImageSrc = computed(() => {
  if (photoPreview.value) {
    // If it's a blob URL (from file upload), use it directly
    if (photoPreview.value.startsWith('blob:')) {
      return photoPreview.value
    }
    // If it's a publicId, construct Cloudinary URL
    return `https://res.cloudinary.com/dave-collison/image/upload/w_200,h_200,c_fill,f_auto,q_auto/${photoPreview.value}`
  }
  return null
})

const handlePhotoChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    photoFile.value = file
    form.value.photoId = ''
    photoPreview.value = URL.createObjectURL(file)
  }
}

const generatedStageName = computed((): string => {
  return [form.value.firstName, form.value.lastName].join(' ').trim() || ''
})

const uploadToCloudinary = async (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('upload_preset', 'artist-photos')

  try {
    const response = await fetch(
      'https://api.cloudinary.com/v1_1/dave-collison/image/upload',
      {
        method: 'POST',
        body: formData
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Upload error details:', errorData)
      throw new Error(`Upload failed: ${errorData.error?.message || 'Unknown error'}`)
    }

    const data = await response.json()
    // Return the public_id instead of secure_url
    return data.public_id
  } catch (err) {
    console.error('Cloudinary upload error:', err)
    throw err
  }
}

const fetchArtistData = async (artistId: string) => {
  isLoadingArtist.value = true
  loadError.value = null

  try {
    const artistData = await getArtist(artistId)
    if (artistData) {
      artist.value = artistData
    } else {
      loadError.value = 'Artist not found'
    }
  } catch (err) {
    console.error('Error fetching artist:', err)
    loadError.value = (err as Error).message
  } finally {
    isLoadingArtist.value = false
  }
}

const handleSubmit = async () => {
  isSubmitting.value = true
  submitError.value = null

  try {
    let photoPublicId: string | null = null

    // Only upload new photo if a file was selected
    if (photoFile.value) {
      photoPublicId = await uploadToCloudinary(photoFile.value)
    } else if (isEditing.value && photoPreview.value) {
      // Keep existing photo publicId if editing and no new file selected
      photoPublicId = photoPreview.value
    }

    // Require photo for new artists only
    if (!isEditing.value && !photoPublicId) {
      throw new Error('Please select a photo')
    }

    const artistData = {
      firstName: form.value.firstName,
      lastName: form.value.lastName,
      stageName: form.value.stageName || generatedStageName.value,
      instruments: form.value.instruments
        ? form.value.instruments.split(',').map((i: string) => i.trim()).filter((i: string) => i.length > 0)
        : [],
      photoId: photoPublicId, // This now stores the publicId, but keeps the field name
    }

    if (isEditing.value) {
      // Update existing artist
      const artistRef = doc(db, 'artists', artistId.value)
      await updateDoc(artistRef, {
        ...artistData,
        updatedAt: Timestamp.now()
      })
    } else {
      // Create new artist
      await addDoc(collection(db, 'artists'), {
        ...artistData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      })
    }

    router.push('/artists')
  } catch (err) {
    console.error('Error saving artist:', err)
    submitError.value = (err as Error).message
  } finally {
    isSubmitting.value = false
  }
}

// Watch for artist data to be loaded and populate form
watch(artist, (newArtist) => {
  if (newArtist) {
    form.value = {
      firstName: newArtist.firstName || '',
      lastName: newArtist.lastName || '',
      stageName: newArtist.stageName || '',
      instruments: newArtist.instruments ? newArtist.instruments.join(', ') : '',
      photoId: newArtist.photoId || ''
    }

    // photoPreview.value = newArtist.photoId ? `https://res.cloudinary.com/dave-collison/image/upload/w_200,h_200,c_fill,f_auto,q_auto/${newArtist.photoId}` : null

    if (newArtist.photoId) {
      photoPreview.value = newArtist.photoId
    }
  }
}, { immediate: true })

onMounted(async () => {
  if (isEditing.value) {
    await fetchArtistData(artistId.value)
  }
})
</script>

<template>
  <header class="page-header">
    <RouterLink to="/artists" class="back-button">
      ← Artists
    </RouterLink>
    <h1>{{ isEditing ? 'Edit' : 'Create New' }} Artist</h1>
  </header>

  <!-- Loading state -->
  <div v-if="isEditing && isLoadingArtist" class="loading-container">
    <p>Loading artist data...</p>
  </div>

  <!-- Error state -->
  <div v-else-if="isEditing && loadError" class="error-container">
    <p class="error-message">{{ loadError }}</p>
    <RouterLink to="/artists" class="back-button">← Back to Artists</RouterLink>
  </div>

  <!-- Form -->
  <form v-else @submit.prevent="handleSubmit" class="artist-form">
    <div class="form-group photo-upload">
      <label for="photo">Profile Photo</label>
      <div class="photo-preview" :class="{ 'has-photo': previewImageSrc }">
        <img v-if="previewImageSrc" :src="previewImageSrc" alt="Photo preview" />
        <span v-else class="photo-placeholder">Click to add photo</span>
        <input type="file" id="photo" accept="image/*" @change="handlePhotoChange" class="photo-input" />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="firstName">First Name</label>
        <input type="text" id="firstName" v-model="form.firstName" required />
      </div>

      <div class="form-group">
        <label for="lastName">Last Name</label>
        <input type="text" id="lastName" v-model="form.lastName" required />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="stageName">Stage Name (optional)</label>
        <input type="text" id="stageName" v-model="form.stageName" :placeholder="generatedStageName" />
      </div>

      <div class="form-group">
        <label for="instruments">Instruments (comma-separated)</label>
        <input type="text" id="instruments" v-model="form.instruments" placeholder="e.g. guitar, vocals, piano"
          required />
      </div>
    </div>

    <div class="form-actions">
      <RouterLink to="/artists" class="cancel-button">
        Cancel
      </RouterLink>
      <BaseButton type="submit" class="submit-button" :disabled="isSubmitting">
        <span v-if="isSubmitting" class="loading-spinner"></span>
        {{ isSubmitting ? (isEditing ? 'Updating Artist...' : 'Creating Artist...') : (isEditing ? 'Update Artist' :
          'Create Artist') }}
      </BaseButton>
    </div>

    <p v-if="submitError" class="error-message">
      {{ submitError }}
    </p>
  </form>
</template>

<style scoped>
.page-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.back-button {
  padding: 0.3em 0.8em;
  border-radius: 0.5em;
  text-decoration: none;
  color: var(--color-accent);
  background-color: var(--color-background-mute);
  transition: all 0.2s ease;
  font-size: var(--step--2);
}

.back-button:hover {
  background-color: var(--color-accent);
  color: var(--color-background);
}

.artist-form {
  background-color: var(--color-background);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 0;
}

.photo-upload {
  text-align: center;
  margin-bottom: 1rem;
}

.photo-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 2px dashed var(--color-border);
  margin: 0.5rem auto;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: var(--color-background-soft);
  transition: all 0.2s ease;
}

.photo-preview:hover {
  border-color: var(--color-accent);
}

.photo-preview.has-photo {
  border-style: solid;
}

.photo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-placeholder {
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

.photo-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-muted);
  font-size: var(--step--1);
}

input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background-color: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button,
.submit-button {
  padding: 0.3em 0.8em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: var(--step--2);
}

.cancel-button {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

.submit-button {
  background-color: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid var(--color-background);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  color: rgb(239, 68, 68);
  font-size: var(--step--1);
  margin-top: 1rem;
  text-align: center;
}

.loading-container,
.error-container {
  text-align: center;
  padding: 2rem;
  background-color: var(--color-background);
  border-radius: 1rem;
  box-shadow: var(--shadow);
}

.error-container .back-button {
  margin-top: 1rem;
  display: inline-block;
}
</style>
