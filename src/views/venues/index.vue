<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { collection, getDocs } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { Venue } from '@/models/Venue'
import VenueList from '@/components/venues/VenueList.vue'
import { Search, Plus } from 'lucide-vue-next'

const { db } = useFirebase()
const venues = ref<Venue[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)
const searchQuery = ref('')

const filteredVenues = computed(() => {
  if (!searchQuery.value) return venues.value

  const query = searchQuery.value.toLowerCase()
  return venues.value.filter((venue: Venue) => {
    const nameMatch = venue.getName().toLowerCase().includes(query)
    const addressMatch = venue.getFormattedAddress().toLowerCase().includes(query)
    return nameMatch || addressMatch
  })
})

async function fetchVenues() {
  try {
    const querySnapshot = await getDocs(collection(db, 'venues'))
    venues.value = querySnapshot.docs.map(doc => Venue.fromFirestore(doc))
  } catch (e) {
    console.error('Error fetching venues:', e)
    error.value = 'Failed to load venues'
  } finally {
    isLoading.value = false
  }
}

onMounted(fetchVenues)
</script>

<template>
  <BaseSection minimal>
    <template #header>
      <div class="page-header">
        <h1>Venues</h1>

        <!-- TODO: Find a better way to implement this link and keep it looking like a primary button -->
        <RouterLink :to="{ name: 'venues.create' }">
          <BaseButton purpose="primary">
            <Plus class="icon" />
            Add Venue
          </BaseButton>
        </RouterLink>
      </div>
    </template>

    <div class="search-container">
      <div class="search-input">
        <Search class="search-icon" />
        <input type="text" v-model="searchQuery" placeholder="Search venues by name or address..." />
      </div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <p v-else-if="isLoading" class="loading">
      Loading venues...
    </p>

    <div v-else-if="!venues.length" class="empty-state">
      <p>No venues found</p>
      <BaseButton purpose="primary" :to="{ name: 'venues.create' }">
        <Plus class="icon" />
        Add your first venue
      </BaseButton>
    </div>

    <div v-else-if="!filteredVenues.length" class="empty-state">
      <p>No venues match your search</p>
      <BaseButton @click="searchQuery = ''" purpose="secondary">
        Clear search
      </BaseButton>
    </div>

    <VenueList v-else :venues="filteredVenues" />
  </BaseSection>
</template>

<style scoped>
.venues {
  list-style: none;
  display: grid;
  gap: .8rem 1rem;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  grid-template-rows: 1fr auto;
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.venues__item {
  display: grid;
  grid-template-rows: subgrid;
  grid-row: span 2;
}

.venue-card {
  display: grid;
  grid-template-rows: subgrid;
  grid-row: span 2;
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.venue-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.venue-card__title {
  color: var(--color-accent);
  margin: 0;
}

.venue-card__details {
  display: flex;
  justify-content: center;
  font-size: var(--step--1);
}

.venue-card__contact {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: var(--step--1);
}

.venue-card__link {
  color: var(--color-text-muted);
  text-decoration: none;
}

.venue-card__link:hover {
  text-decoration: underline;
}

.venue-card__actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  font-size: var(--step--2);

}

.venue-card__actions a {
  color: var(--color-text-muted);
  text-decoration: none;
  padding: 0.3em 0.8em;
  border-radius: 0.5em;
  transition: background-color 0.2s ease;
}

.venue-card__actions a:hover {
  background-color: var(--color-background-mute);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  margin: 0;
}

.search-container {
  margin-bottom: 2rem;
}

.search-input {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.2em;
  height: 1.2em;
  color: var(--color-text-light);
}

.search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--step--1);
}

.search-input input:focus {
  outline: none;
  border-color: var(--color-accent);
}

.search-input input::placeholder {
  color: var(--color-text-light);
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--color-text-light);
}

.empty-state .action-button {
  margin-top: 1rem;
}
</style>
