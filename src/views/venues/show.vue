<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useVenues } from '@/composables/venues/useVenues'
import { MapPin, Phone, Mail, Globe, ArrowLeft, Calendar, PoundSterling, PlusCircle, Pencil, Trash2 } from 'lucide-vue-next'
import { Event } from '@/models/Event'

const router = useRouter()
const route = useRoute()
const {
  currentVenue: venue,
  isLoadingVenue: isLoading,
  subscribeToVenue,
  cleanupVenues: cleanup,
  fetchVenueEvents,
  deleteVenue
} = useVenues()

const events = ref<Event[]>([])

// Add handler for delete action
async function handleDelete() {
  if (!venue.value) return
  const success = await deleteVenue(venue.value.id)
  if (success) {
    router.push({ name: 'venues.index' })
  }
}

// Add handler for edit action
function handleEdit() {
  if (!venue.value) return
  router.push({ name: 'venues.edit', params: { id: venue.value.id } })
}

// Computed properties for statistics
const totalBookings = computed(() => events.value.length)

onMounted(async () => {
  const venueId = route.params.id
  if (typeof venueId !== 'string') {
    // Handle error
    return
  }

  // Subscribe to the venue and fetch its events
  subscribeToVenue(venueId)

  // Fetch events for this venue
  try {
    events.value = await fetchVenueEvents(venueId)
  } catch (error) {
    console.error('Error fetching venue events:', error)
  }
})

onUnmounted(() => {
  cleanup()
})

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
  }).format(amount)
}

// Function to get next viable date with same time
function getNextViableDate(lastDate: Date) {
  const nextDate = new Date(lastDate)
  const now = new Date()

  // Set minimum 24 hours in the future
  const minDate = new Date(now)
  minDate.setHours(now.getHours() + 24)

  // If the next date with same time is less than 24 hours away,
  // move it to the next day
  while (nextDate < minDate) {
    nextDate.setDate(nextDate.getDate() + 1)
  }

  return nextDate
}

// Function to create new event from last gig
async function createFromLastGig() {
  // If this venue has no events, let's open the new event form populated with this venue's details
  if (!events.value.length) {
    router.push({ name: 'events.create', query: { venue: venue.value?.id } })
    return
  }

  // Get next viable date with same time as last gig
  const lastGig = events.value.reduce((last, current) => {
    return current.when.toDate() > last.when.toDate() ? current : last
  }, events.value[0])
  const nextDate = getNextViableDate(lastGig.when.toDate())

  // Get the template data from last gig - only copy the fields we need
  const templateData = {
    title: lastGig.title || '',
    description: lastGig.description || '',
    duration: lastGig.duration || 180,
    when: nextDate.toISOString(), // Convert to string for JSON serialization
    venue: venue.value?.id,
    acts: [...(lastGig.acts || [])], // Create new array copy
    isPrivate: lastGig.isPrivate || false,
    status: 'draft',
    notes: {
      fee: {
        amount: lastGig.notes?.fee.amount || venue.value?.notes?.fee || null,
        paid: false,
        date: null
      },
      deposit: {
        amount: lastGig.notes?.deposit.amount || venue.value?.notes?.deposit || null,
        paid: false,
        date: null
      },
      agent: lastGig.notes?.agent || venue.value?.notes?.agentId || null
    }
  }

  // Navigate to create event with template data
  router.push({
    name: 'events.create',
    query: {
      template: btoa(JSON.stringify(templateData))
    }
  })
}
</script>

<template>
  <div class="venue-show-page">
    <RouterLink :to="{ name: 'venues.index' }" class="back-button">
      <ArrowLeft class="icon" />
      Back to Venues
    </RouterLink>

    <div v-if="isLoading" class="loading">
      <LoadingSpinner />
    </div>

    <div v-else-if="!venue" class="error-message">
      Venue not found
    </div>

    <BaseSection v-else minimal title="Venue Details">
      <hr>
      <header class="venue-header">
        <h3>{{ venue.getName() }}</h3>
        <BaseButton @click="createFromLastGig" title="Book again with same details" size="compact" purpose="primary">
          <PlusCircle class="icon" />
          <span>Rebook</span>
        </BaseButton>
      </header>

      <div class="venue-detail">
        <MapPin class="icon" />
        <span>{{ venue.getFormattedAddress() }}</span>
      </div>

      <div class="venue-detail" v-if="venue.phone">
        <Phone class="icon" />
        <a :href="`tel:${venue.phone}`">{{ venue.phone }}</a>
      </div>

      <div class="venue-detail" v-if="venue.email">
        <Mail class="icon" />
        <a :href="`mailto:${venue.email}`">{{ venue.email }}</a>
      </div>

      <div class="venue-detail" v-if="venue.website">
        <Globe class="icon" />
        <a :href="venue.website" target="_blank" rel="noopener noreferrer">
          {{ venue.website }}
        </a>
      </div>

      <hr>

      <div class="venue-detail" v-if="events.length">
        <Calendar class="icon" />
        <div class="last-gig">
          <RouterLink :to="{ name: 'events.show', params: { id: events[events.length - 1].id } }">
            Last booking: {{ events[events.length - 1].formattedDate() }}
          </RouterLink>
        </div>
      </div>

      <div class="venue-detail">
        <PoundSterling class="icon" />
        <details>
          <summary>
            {{formatCurrency(events.reduce((sum, event) => sum + (event.fee() || 0), 0))}} total revenue <span
              class="small-text">({{ totalBookings }} bookings)</span>
          </summary>
          <div class="event-list shadow">
            <RouterLink v-for="event in events" :key="event.id" :class="{ 'no-fee': !event.fee() }"
              :to="{ name: 'events.show', params: { id: event.id } }" class="event-strip">
              <span class="event-date">{{ event.formattedDate() }} @ {{ event.startTime() }}</span>
              <span class="event-fee tabular">{{ event.fee() ? formatCurrency(event.fee()) : 'No fee set' }}</span>
            </RouterLink>
          </div>
        </details>
      </div>

      <div class="actions">
        <BaseButton @click="handleEdit" purpose="primary" aria-label="Edit Venue" title="Edit Venue">
          <Pencil class="icon" />
        </BaseButton>
        <BaseButton @click="handleDelete" purpose="danger" aria-label="Delete Venue" title="Delete Venue">
          <Trash2 class="icon" />
        </BaseButton>
      </div>
    </BaseSection>

  </div>
</template>
<style scoped>
.venue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-xs);
}

h3 {
  font-size: var(--step-1);
}

.venue-detail {
  display: flex;
  align-items: center;
  gap: var(--space-3xs);
  margin: 0;
  line-height: 1;

  &:has(details) {
    align-items: flex-start;
  }
}

.last-gig {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

details {
  padding: 0 var(--space-xs);

  summary {
    margin-block-end: var(--space-xs);
  }
}

.event-list {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0 var(--space-xl);
  border-radius: var(--radius-m);
  overflow: hidden;

  .event-strip {
    display: grid;
    grid-template-columns: subgrid;
    grid-column: span 2;
    padding: var(--space-xs);
    background-color: var(--color-bg-1);
    border-radius: 0;

    &:nth-child(odd) {
      background-color: var(--color-bg-2);
    }
  }
}

.tabular {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
</style>
