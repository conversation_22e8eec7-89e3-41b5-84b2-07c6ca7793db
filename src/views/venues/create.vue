<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useVenues } from '@/composables/venues/useVenues'
import { Venue } from '@/models/Venue'
import VenueForm from '@/components/venues/VenueForm.vue'

const router = useRouter()

const isLoading = ref(false)
const error = ref<string | null>(null)

const handleSubmit = async (formData: Partial<Venue>) => {
  try {
    isLoading.value = true
    error.value = null

    const { createVenue } = useVenues()
    const venueId = await createVenue(formData)

    router.push({ name: 'venues.show', params: { id: venueId } })
  } catch (e) {
    console.error('Error creating venue:', e)
    error.value = 'Failed to create venue'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <header class="page-header">
    <h1>Create Venue</h1>
  </header>

  <VenueForm @submit="handleSubmit" :isLoading="isLoading" />

  <p v-if="error" class="error-message">{{ error }}</p>
</template>

<style scoped>
.page-header {
  margin-bottom: 2rem;
}

.venue-form {
  max-width: 800px;
  width: 90%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1.5rem;
}

.form-section h2 {
  color: var(--color-accent);
  margin-bottom: 1.5rem;
  font-size: var(--step-0);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--color-text);
  font-size: var(--step--1);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  background: var(--color-background);
  color: var(--color-text);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  cursor: pointer;
  font-size: var(--step--1);
  transition: all 0.2s ease;
}

.button--primary {
  background: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}

.button--primary:hover {
  filter: brightness(1.1);
}

.button--secondary {
  background: var(--color-background-mute);
  color: var(--color-text);
}

.button--secondary:hover {
  background: var(--color-background-soft);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-message {
  color: var(--color-text-danger);
  text-align: center;
  margin-top: 1rem;
}

.required {
  color: var(--color-accent);
  margin-left: 0.25em;
}
</style>
