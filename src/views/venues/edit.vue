<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Venue } from '@/models/Venue'
import { useVenues } from '@/composables/venues/useVenues'
import { ArrowLeft } from 'lucide-vue-next'
import VenueForm from '@/components/venues/VenueForm.vue'

const router = useRouter()
const route = useRoute()
const { fetchVenue } = useVenues()

const venue = ref<Venue | null>(null)

const isLoading = ref(true)
const error = ref<string | null>(null)

// Handle form submission
const handleSubmit = async (formData: Partial<Venue>) => {
  if (!venue.value) return

  try {
    isLoading.value = true
    error.value = null

    // Update venue using the model's update method
    await venue.value.update(formData)

    router.push({
      name: 'venues.show',
      params: { id: venue.value.id }
    })
  } catch (e) {
    console.error('Error updating venue:', e)
    error.value = 'Failed to update venue'
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  const id = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;

  if (typeof id !== 'string') {
    error.value = 'Invalid venue ID'
    isLoading.value = false
    return
  }

  console.log('Fetching venue with ID:', id)

  venue.value = await fetchVenue(id)

  isLoading.value = false
})
</script>

<template>
  <div>
    <header class="page-header">
      <RouterLink :to="{ name: 'venues.show', params: { id: route.params.id } }" class="back-button">
        <ArrowLeft class="icon" />
        <span>Back to Venue</span>
      </RouterLink>
      <h1>Edit Venue</h1>
    </header>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <p v-else-if="isLoading" class="loading">
      Loading venue...
    </p>

    <VenueForm v-else class="edit-form" :venue="venue || undefined" :isLoading="isLoading" @submit="handleSubmit" />
  </div>
</template>

<style scoped>
.page-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.page-header h1 {
  margin: 0;
  color: var(--color-heading);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-light);
  text-decoration: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: var(--color-background-soft);
  color: var(--color-text);
}

.edit-form {
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  gap: 2rem;
}

.error-message {
  text-align: center;
  color: var(--color-text-danger);
  margin: 2rem 0;
}

.loading {
  text-align: center;
  color: var(--color-text-light);
  margin: 2rem 0;
}

.icon {
  width: 1.2em;
  height: 1.2em;
  flex-shrink: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex: 0 0 auto;
}

.notes-container {
  flex: 1;
}

.add-note-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.new-note-form {
  padding: 1rem;
  margin-bottom: 1rem;
}

.note-title-input {
  margin-bottom: 1rem;
}

.note-content-editor {
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.note-item {
  padding: 0;
}

.note-details {
  padding: 1rem;
}

.note-header {
  display: flex;
  gap: var(--space-s);
  align-items: flex-end;
  margin-bottom: 0.5rem;
}

.note-title {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.note-title h3 {
  margin: 0;
  font-size: var(--step-0);
  color: var(--color-heading);
  font-weight: 500;
}

.note-metadata {
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  gap: var(--space-s);
  color: var(--color-text-muted);
  font-size: var(--step--2);
}

.note-author {
  font-weight: 500;
}

.note-timestamp {
  font-style: italic;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
  margin-inline-start: auto;
}

.note-content {
  margin-top: 0.75rem;
  color: var(--color-text);
  line-height: 1.5;
}

.note-content :deep(p) {
  margin: 0.5rem 0;
}

.note-content :deep(p:first-child) {
  margin-top: 0;
}

.note-content :deep(p:last-child) {
  margin-bottom: 0;
}

.no-notes {
  color: var(--color-text-light);
  font-style: italic;
}

.delete-dialog :deep(.dialog-content) {
  max-width: 400px;
}

.note-title-preview {
  margin-top: 0.5rem;
  font-weight: 500;
  color: var(--color-heading);
}

.note-details {
  background: var(--color-background-soft);
  border-radius: 0.5rem;
  padding: 1rem;
}

.note-details summary {
  list-style: none;
  cursor: pointer;
}

.note-details summary::-webkit-details-marker {
  display: none;
}

.note-details[open] summary {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border);
}

.note-details:not([open]) .note-header {
  margin-bottom: 0;
}
</style>
