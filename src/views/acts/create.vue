<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useActs } from '@/composables/acts/useActs'
import ActForm from '@/components/acts/ActForm.vue'
import { Act } from '@/models/Act'

const router = useRouter()
const { isLoading: isLoadingActs, createAct, subscribeToActs, cleanup } = useActs()
const isLoading = ref(false)
const error = ref<string | null>(null)

async function handleSubmit(data: Act) {
  isLoading.value = true
  error.value = null

  try {
    await createAct(data)
    router.push({ name: 'acts.index' })
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to create act'
    console.error('Error creating act:', err)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <header class="section-header">
    <div class="section-header__title">
      <h1>Create New Act</h1>
      <p class="section-header__subtitle">Add a new entertainment act</p>
    </div>
  </header>

  <p v-if="error" class="error-message">{{ error }}</p>

  <ActForm @submit="handleSubmit" />
</template>

<style scoped>
.section-header {
  margin-bottom: 2rem;
}

.section-header__subtitle {
  color: var(--color-text-muted);
  margin-top: 0.25rem;
}

.error-message {
  color: var(--color-error);
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .create-act-page {
    padding: 1rem;
  }
}
</style>
