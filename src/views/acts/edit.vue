<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useActs } from '@/composables/acts/useActs'
import ActForm from '@/components/acts/ActForm.vue'
import type { Act } from '@/models/Act'

const props = defineProps<{
  id: string
}>()

const router = useRouter()
const { fetchAct, updateAct } = useActs()
const act = ref<Act | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

onMounted(async () => {
  try {
    const foundAct = await fetchAct(props.id)
    if (foundAct) {
      act.value = foundAct
    } else {
      error.value = 'Act not found'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load act'
  } finally {
    isLoading.value = false
  }
})

async function handleSubmit(data: Act) {
  try {
    await updateAct(props.id, data)
    router.push({ name: 'acts.show', params: { id: props.id } })
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to update act'
  }
}
</script>

<template>
  <header class="section-header">
    <div class="section-header__title">
      <h1>Edit Act</h1>
      <p class="section-header__subtitle" v-if="act">{{ act.name }}</p>
    </div>
  </header>

  <div v-if="isLoading" class="loading-state">
    <LoadingSpinner />
    <p>Loading act...</p>
  </div>

  <p v-else-if="error" class="error-message">{{ error }}</p>

  <ActForm v-else :initial-values="{
    name: act?.name || '',
    description: act?.description || '',
    defaultGigDescription: act?.defaultGigDescription || '',
    displayName: act?.displayName || undefined,
    website: act?.website || undefined,
    logoUrls: act?.logoUrls,
    photoUrl: act?.photoUrl || undefined,
    artistIds: act?.artistIds
  }" @submit="handleSubmit" />
</template>

<style scoped>
.section-header {
  margin-bottom: 2rem;
}

.section-header__subtitle {
  color: var(--color-text-muted);
  margin-top: 0.25rem;
}

.error-message {
  color: var(--color-error);
  margin: 1rem 0;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
}

@media (max-width: 768px) {
  .section-header__title {
    padding: 1rem;
  }
}
</style>
