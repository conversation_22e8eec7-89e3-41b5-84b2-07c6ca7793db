<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { collection, query, where, orderBy, onSnapshot, type Unsubscribe } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'

const { db } = useFirebase()
const events = ref<any[]>([])
const isLoading = ref(true)
const allFields = ref<Set<string>>(new Set())
let unsubscribe: Unsubscribe | null = null

const subscribeToRawEvents = () => {
  try {
    const eventsRef = collection(db, 'events')
    const q = query(
      eventsRef,
      where('when', '>=', new Date()),
      orderBy('when', 'asc')
    )

    unsubscribe = onSnapshot(q, (snapshot) => {
      // Reset fields for each update
      allFields.value.clear()

      // First pass: collect all possible fields
      snapshot.docs.forEach(doc => {
        const data = doc.data()
        collectFields(data)
      })

      // Second pass: create events with all known fields
      events.value = snapshot.docs.map(doc => {
        const data = doc.data()
        return {
          _docId: doc.id,
          ...Object.fromEntries(
            Array.from(allFields.value).map(field => [
              field,
              getNestedValue(data, field)
            ])
          )
        }
      })

      isLoading.value = false
    }, (error) => {
      console.error('Error in events subscription:', error)
      isLoading.value = false
    })
  } catch (error) {
    console.error('Error setting up events subscription:', error)
    isLoading.value = false
  }
}

// Cleanup function
const cleanup = () => {
  if (unsubscribe) {
    unsubscribe()
    unsubscribe = null
  }
}

onMounted(() => {
  subscribeToRawEvents()
})

onUnmounted(() => {
  cleanup()
})

// Recursively collect only top-level fields and objects
const collectFields = (obj: any, prefix = '') => {
  Object.entries(obj).forEach(([key, value]) => {
    const fullKey = prefix ? `${prefix}.${key}` : key

    // Only add the field if it's either:
    // 1. A top-level field, or
    // 2. Not an object (to avoid collecting nested paths)
    if (!prefix || (value !== null && typeof value !== 'object')) {
      allFields.value.add(fullKey)
    }
  })
}

// Get nested value using dot notation
const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, part) => {
    return current && current[part] !== undefined ? current[part] : null
  }, obj)
}

function fieldClasses(value: string): object {
  if (value === null) return { 'value-missing': true }
  if (value === 'null') return { 'value-null': true }
  if (value.startsWith('"') && value.endsWith('"')) return { 'value-string': true }
  if (value.includes('\n')) return { 'value-object': true }
  if (!isNaN(value as any) && value !== 'null') return { 'value-number': true }
  if (value === 'true' || value === 'false') return { 'value-boolean': true }
  return {}
}

const formatValue = (value: any, indent = 0): { raw: string; timestamp?: string; type: string } => {
  if (value === null) return { raw: 'null', type: 'null' }

  // Check for Firestore Timestamp
  if (value && typeof value === 'object' && 'seconds' in value && 'nanoseconds' in value) {
    const date = new Date(value.seconds * 1000)
    return {
      raw: JSON.stringify(value),
      timestamp: date.toLocaleString('en-GB', {
        dateStyle: 'medium',
        timeStyle: 'medium'
      }),
      type: 'timestamp'
    }
  }

  if (value instanceof Date) return { raw: value.toISOString(), type: 'string' }
  if (typeof value === 'string') return { raw: `"${value}"`, type: 'string' }
  if (typeof value === 'number') return { raw: String(value), type: 'number' }
  if (typeof value === 'boolean') return { raw: String(value), type: 'boolean' }

  if (Array.isArray(value)) {
    if (value.length === 0) return { raw: '[]', type: 'array' }
    const items = value.map(v => {
      const formatted = formatValue(v, indent + 2)
      return `<span class="value-${formatted.type}">${formatted.raw}</span>`
    }).join('\n' + ' '.repeat(indent + 2))
    return { raw: `\n${' '.repeat(indent + 2)}${items}`, type: 'array' }
  }

  if (typeof value === 'object') {
    if (Object.keys(value).length === 0) return { raw: '', type: 'object' }
    const entries = Object.entries(value)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => {
        const formatted = formatValue(v, indent + 2)
        if (formatted.raw.includes('\n')) {
          return `${k}:\n${' '.repeat(indent + 2)}<span class="value-${formatted.type}">${formatted.raw.trim()}</span>`
        }
        return `${k}: <span class="value-${formatted.type}">${formatted.raw}</span>`
      }).join('\n' + ' '.repeat(indent))
    return { raw: entries, type: 'object' }
  }

  return { raw: String(value), type: 'string' }
}

// Filter out nested paths when they're part of an expanded object
const formatEventData = (event: any) => {
  const fields = Array.from(allFields.value).sort()
  const expandedPaths = new Set()

  // First, identify all expanded object paths
  fields.forEach(field => {
    const value = getNestedValue(event, field)
    if (value !== null && typeof value === 'object') {
      expandedPaths.add(field)
    }
  })

  // Then filter out fields that are children of expanded paths
  const filteredFields = fields.filter(field => {
    return !Array.from(expandedPaths).some(path =>
      field !== path && field.startsWith(path + '.')
    )
  })

  return filteredFields.map(field => ({
    key: field,
    ...formatValue(getNestedValue(event, field))
  }))
}
</script>

<template>
  <header>
    <h1>Events (Dev View)</h1>
    <div v-if="isLoading">Loading...</div>
    <div v-else class="event-count">{{ events.length }} events (live)</div>
  </header>

  <div class="events-container">
    <div v-for="event in events" :key="event._docId" class="event-card">
      <div class="event-header">
        <div class="event-id">{{ event._docId }}</div>
      </div>
      <div class="event-data">
        <div v-for="(field, index) in formatEventData(event)" :key="index" class="field-row">
          <span class="field-name">{{ field.key }}</span>
          <div class="field-value-container">
            <pre :class="['field-value']" v-html="field.raw"></pre>
            <span v-if="field.timestamp" class="timestamp-value">
              {{ field.timestamp }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
header {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.event-count {
  font-size: 0.9em;
  color: #666;
}

.events-container {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 1rem;
}

.event-card {
  flex: 0 0 auto;
  width: 300px;
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.event-header {
  padding: 0.5rem;
  min-height: 6em;
  background: var(--color-background-mute);
  border-bottom: 1px solid var(--color-border);
}

.event-id {
  font-weight: 500;
  word-wrap: break-word;
  white-space: normal;
  line-height: 1.2;
}

.event-data {
  padding: 0.5rem;
  margin: 0;
  font-family: monospace;
  font-size: 12px;
}

.field-row {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.5rem;
  align-items: start;
  padding: 4px 0;
}

.field-name {
  color: var(--color-primary);
  font-weight: 500;
}

.field-value-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-width: 100%;
  overflow-x: auto;
}

.field-value {
  word-break: break-word;
  white-space: pre;
  margin: 0;
  font-family: monospace;
  padding-left: 1rem;
}

.field-value :deep(.value-null) {
  color: #888;
  font-style: italic;
}

.field-value :deep(.value-string) {
  color: #22863a;
}

.field-value :deep(.value-number) {
  color: #005cc5;
}

.field-value :deep(.value-boolean) {
  color: #e36209;
}

.field-value :deep(.value-array) {
  color: #6f42c1;
}

.field-value :deep(.value-object) {
  color: var(--color-text);
}

.field-value :deep(.value-missing) {
  color: var(--color-danger, #dc3545);
  font-style: italic;
}

.timestamp-value {
  color: var(--color-warning, #ff9800);
  font-size: 0.9em;
  font-style: italic;
}

.events-container::-webkit-scrollbar {
  height: 8px;
}

.events-container::-webkit-scrollbar-track {
  background: var(--color-background-mute);
  border-radius: 4px;
}

.events-container::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

.events-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-heading);
}
</style>
