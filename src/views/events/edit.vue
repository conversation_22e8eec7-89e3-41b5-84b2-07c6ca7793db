<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useEvents } from '@/composables/events/useEvents'
import EventForm from '@/components/events/EventForm.vue'
import { Event, type EventNotes } from '@/models/Event'
import { Timestamp } from 'firebase/firestore'

const route = useRoute()
const router = useRouter()
const { createEvent, updateEvent, subscribeToEvent, currentEvent, isLoading, areDetailsLoading } = useEvents()
const isSubmitting = ref(false)

const isEditMode = computed(() => !!route.params.id)
const pageTitle = computed(() => {
  if (isEditMode.value) return 'Edit Event'
  if (templateData.value) return 'Create Event from Template'
  return 'Create Event'
})

// Handle template data from query params
const templateData = ref<Partial<Event> | undefined>(undefined)

// Update document title
watch(() => pageTitle.value, (newTitle) => {
  document.title = newTitle
}, { immediate: true })

onMounted(async () => {
  if (isEditMode.value) {
    const eventId = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id
    await subscribeToEvent(eventId)
  } else {
    // Check for template data in query params
    if (route.query.template) {
      try {
        const decodedTemplate = atob(route.query.template as string)
        const parsed = JSON.parse(decodedTemplate)

        // Convert date to Firestore Timestamp if it exists
        if (parsed.when) {
          parsed.when = Timestamp.fromDate(new Date(parsed.when))
        }

        // Sanitize the data - only keep the fields we expect
        const sanitizedData: Partial<Event> = {
          title: parsed.title || '',
          description: parsed.description || '',
          duration: parsed.duration || 180,
          when: parsed.when,
          venue: parsed.venue || '',
          acts: Array.isArray(parsed.acts) ? [...parsed.acts] : ['daves-roy-orbison'],
          isPrivate: Boolean(parsed.isPrivate),
          status: parsed.status || 'draft',
          notes: parsed.notes ? {
            fee: {
              amount: parsed.notes.fee?.amount || null,
              paid: Boolean(parsed.notes.fee?.paid),
              date: parsed.notes.fee?.date || null
            },
            deposit: {
              amount: parsed.notes.deposit?.amount || null,
              paid: Boolean(parsed.notes.deposit?.paid),
              date: parsed.notes.deposit?.date || null
            },
            agent: typeof parsed.notes.agent === 'string' ? parsed.notes.agent : null
          } : undefined
        }

        templateData.value = sanitizedData
      } catch (error) {
        console.error('Failed to parse template data:', error)
      }
    }
    isLoading.value = false
  }
})

const showForm = computed(() => {
  if (isEditMode.value) {
    return !areDetailsLoading.value && currentEvent.value !== undefined
  }
  // For create mode, only show form when not loading
  // If there's a template query param, wait for template data to be loaded
  if (route.query.template) {
    return !isLoading.value && templateData.value !== undefined
  }
  return !isLoading.value
})

const handleSubmit = async (eventData: Partial<Event>) => {
  isSubmitting.value = true
  try {
    const eventId = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id

    // Prepare the notes data ensuring proper typing and structure
    const notesData: EventNotes = {
      fee: {
        amount: eventData.notes?.fee?.amount !== undefined ? Number(eventData.notes.fee.amount) : 0,
        paid: eventData.notes?.fee?.paid ?? false,
        date: eventData.notes?.fee?.date ?? null
      },
      deposit: {
        amount: eventData.notes?.deposit?.amount !== undefined ? Number(eventData.notes.deposit.amount) : 0,
        paid: eventData.notes?.deposit?.paid ?? false,
        date: eventData.notes?.deposit?.date ?? null
      },
      agent: eventData.notes?.agent ?? null
    }

    const updates: Partial<Event> = {
      ...eventData,
      notes: notesData
    }

    if (isEditMode.value) {
      await updateEvent(eventId, updates)
    } else {
      await createEvent(updates)
    }

    // Only navigate if successful
    router.push('/events')
    return true
  } catch (error) {
    console.error(`Failed to ${isEditMode.value ? 'update' : 'create'} event:`, error)
    return false
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <BaseSection minimal>
    <template #header>
      <div class="section-header">
        <h2>{{ pageTitle }}</h2>
        <RouterLink v-if="isEditMode" :to="{ name: 'events.show', params: { id: route.params.id } }" class="back-link">
          ← Back to Event
        </RouterLink>
      </div>
    </template>

    <LoadingSpinner v-if="isLoading || areDetailsLoading">
      {{ isLoading ? 'Loading event...' : 'Loading event details...' }}
    </LoadingSpinner>

    <EventForm v-if="showForm && !isEditMode" :key="templateData ? 'template' : 'default'"
      :initial-values="templateData as Partial<Event> | undefined" :is-submitting="isSubmitting"
      :date="route.query.date as string | null" @submit="handleSubmit" />

    <EventForm v-else-if="showForm" :initial-values="currentEvent as Partial<Event>" is-edit-mode
      :is-submitting="isSubmitting" @submit="handleSubmit" />

  </BaseSection>
</template>

<style scoped>
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-link {
  font-size: 0.9em;
  color: var(--color-text-secondary);
  text-decoration: none;
}

.back-link:hover {
  color: var(--color-primary);
}
</style>
