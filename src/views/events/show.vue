<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useEvents } from '@/composables/events/useEvents'
import EntityImage from '@/components/base/EntityImage.vue'
import { MapPin, Calendar, Clock, Globe, Mail, Phone, Pencil, Trash2, Eye } from 'lucide-vue-next'

const props = defineProps<{
  id: string
}>()

const router = useRouter()
const {
  currentEvent: event,
  isLoading: isLoadingEvent,
  areDetailsLoading,
  error,
  deleteEvent,
  cleanup,
  subscribeToEvent
} = useEvents()

// Actions
const handleDelete = async () => {
  if (!event.value) return

  const success = await deleteEvent(event.value.id)
  if (success) {
    router.push({ name: 'events.index' })
  }
}

// Lifecycle hooks
onMounted(async () => {
  await subscribeToEvent(props.id)
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <div v-if="isLoadingEvent || areDetailsLoading" class="loading-container">
    <LoadingSpinner />
    <p>{{ isLoadingEvent ? 'Loading event...' : 'Loading event details...' }}</p>
  </div>

  <div v-else-if="error" class="error-container">
    <p class="error-message">{{ error }}</p>
    <BaseButton @click="router.push({ name: 'events.index' })">
      Return to Events
    </BaseButton>
  </div>

  <BaseSection v-else-if="event" class="event-card" minimal>
    <template #header>
      <RouterLink :to="{ name: 'events.index' }" class="back-button">
        ← Events
      </RouterLink>
      <h2 class="event-title" :class="{ 'no-title': !event.title }">
        <span v-if="!event.title">(</span>{{ event.shortTitle() }}<span v-if="!event.title">)</span>
      </h2>
      <div class="date">
        <Calendar class="icon" />
        <span>{{ event.formattedDate() }}</span>
        at
        <Clock class="icon" />
        <span>{{ event.startTime() }}</span>
      </div>
    </template>

    <div class="event-details">
      <div>
        <div class="venue" v-if="event.venueDetails">
          <MapPin class="icon" />
          <a :href="`https://www.google.com/maps/search/?api=1&query=${event.venueName() + ', ' + event.venueAddress()}`"
            target="_blank" rel="noopener noreferrer">
            <span>{{ event.venueName() }},
              {{ event.venueAddress() }}
            </span>
          </a>
        </div>
        <div class="venue-contact">
          <a v-if="event.venueDetails?.website" :href="event.venueDetails.website" target="_blank"
            rel="noopener noreferrer" class="venue-link">
            <Globe class="icon" />
            <span>Website</span>
          </a>
          <a v-if="event.venueDetails?.email" :href="`mailto:${event.venueDetails.email}`" class="venue-link">
            <Mail class="icon" />
            <span>{{ event.venueDetails.email }}</span>
          </a>
          <a v-if="event.venueDetails?.phone" :href="`tel:${event.venueDetails.phone}`" class="venue-link">
            <Phone class="icon" />
            <span>{{ event.venueDetails.phone }}</span>
          </a>
        </div>
      </div>

      <div class="event-acts" v-if="event.actDetails.length">
        <h3>Acts</h3>
        <div v-for="act in event.actDetails" :key="act.id" class="event-act">
          <EntityImage :public-id="act.logoUrls?.badge!" :alt="`${act.displayName} badge`" class="event-act-badge"
            :title="act.displayName" />
          {{ act.displayName }}
        </div>
      </div>
    </div>
    <div class="event-description" v-html="event.description"></div>
    <template #footer>
      <div class="event-footer">
        <small class="event-id">
          <span v-if="event.isPrivate" class="is-private">Private</span>
          ID: {{ event.id }}
        </small>
        <div class="event-actions">
          <BaseButton @click="router.push({ name: 'events.edit', params: { id: event.id } })" purpose="primary"
            aria-label="Edit Event" title="Edit Event">
            <Pencil class="icon" />
          </BaseButton>
          <BaseButton @click="handleDelete" purpose="danger" aria-label="Delete Event" title="Delete Event">
            <Trash2 class="icon" />
          </BaseButton>
        </div>
      </div>
    </template>
    <div class="external-view-links">
      <h3>Links</h3>
      <a :href="`https://davesroyorbison.com/#/events/${event.id}`" target="_blank" rel="noopener noreferrer"
        class="base-button level-0">
        <Eye class="icon" /> davesroyorbison.com
      </a>
      <a v-if="event.acts.find(act => act === 'human-jukebox')" href="https://daveshumanjukebox.com/events"
        target="_blank" rel="noopener noreferrer" class="base-button level-0">
        <Eye class="icon" /> daveshumanjukebox.com
      </a>
      <a v-if="event.acts.find(act => act === 'orbison-project')"
        :href="`https://theorbisonproject.com/#/events/${event.id}`" target="_blank" rel="noopener noreferrer"
        class="base-button level-0">
        <Eye class="icon" /> theorbisonproject.com
      </a>
    </div>
  </BaseSection>
</template>

<style scoped>
.back-button {
  display: inline-block;
  font-size: var(--step-0);
  margin-block-end: var(--space-xs);
}

.event-title {
  font-size: var(--step-2);

  &.no-title {
    color: var(--color-text-soft);
    font-size: var(--step-1);
  }
}


.date {
  display: flex;
  align-items: center;
  gap: var(--space-3xs);
  color: var(--color-text-muted);
}

.event-details {
  display: grid;
  gap: var(--space-s);
  margin-block-end: var(--space-3xs);

  .venue {
    display: flex;
    align-items: center;
    gap: var(--space-3xs);

    >* {
      flex-shrink: 0;
      width: fit-content;
    }
  }

  .event-acts {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--space-2xs);


    .event-act {
      display: flex;
      align-items: center;
      gap: var(--space-2xs);
      font-size: var(--step--1);
      background-color: var(--color-bg-1);
      padding: var(--space-3xs) var(--space-xs);
      border-radius: var(--radius-s);

      .event-act-badge {
        height: 1em;
        width: 1em;
      }
    }
  }

  .venue-contact {
    display: flex;
    flex-wrap: wrap;
    font-size: var(--step--1);
    gap: 0 var(--space-xs);

    >* {
      display: flex;
      align-items: center;
      gap: var(--space-3xs);
    }
  }
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: end;
}

.event-id {
  font-size: var(--step--2);
  color: var(--color-text-muted);
}

.is-private {
  padding: var(--space-3xs) var(--space-2xs);
  background-color: var(--color-danger-dark);
  border-radius: var(--space-3xs);
  color: var(--color-text);
  margin-inline-end: var(--space-3xs);
}

.event-actions {
  display: flex;
  gap: var(--space-2xs);
}

.external-view-links {
  display: flex;
  align-items: center;
  gap: var(--space-2xs);
  width: fit-content;

  a {
    display: flex;
    align-items: center;
    gap: var(--space-2xs);
    text-decoration: none;
    color: var(--color-text);
    font-size: var(--step--1);
    background-color: var(--color-bg-1);
    padding: var(--space-3xs) var(--space-s);
    border-radius: var(--radius-s);

    &:hover {
      background-color: var(--color-bg-4);
      translate: 0 -2px;
      box-shadow: var(--shadow-m);
    }

    .icon {
      color: var(--color-primary-light);
    }
  }
}

h3 {
  font-size: var(--step-0);
  color: var(--color-text-soft);
}
</style>
