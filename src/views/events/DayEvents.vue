<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useEvents } from '@/composables/events/useEvents'
import { Event } from '@/models/Event'
import EventCard from '@/components/events/EventCard.vue'

const route = useRoute()
const { getEventsForDay, isLoading: isLoadingEvents } = useEvents()

const dateString = computed(() => {
  const date = new Date(route.params.date as string)
  return date.toLocaleDateString(undefined, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

const dayEvents = computed<Event[]>(() => {
  const date = new Date(route.params.date as string) // Make sure to properly parse the date
  return getEventsForDay(date) as Event[]
})
</script>

<template>
  <div class="events-controls">
    <RouterLink :to="{ name: 'home' }" class="control-button">
      ← Back
    </RouterLink>
    <RouterLink :to="{ name: 'events.index' }" class="control-button">
      All Events
    </RouterLink>
  </div>
  <header class="events-header">
    <h1>Events on {{ dateString }}</h1>
  </header>

  <p v-if="isLoadingEvents" class="loading">Loading...</p>
  <ul v-else class="events">
    <li v-for="event in dayEvents" :key="event.id" class="events__item">
      <EventCard :event="event" />
    </li>
  </ul>
</template>

<style scoped>
.events-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.control-button {
  padding: 0.3em 0.8em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  background-color: var(--color-background-mute);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--step--2);
  text-decoration: none;
}

.control-button:hover {
  background-color: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}

.events-header {
  margin-bottom: 2rem;
}

.events--grid {
  list-style: none;
  display: grid;
  gap: 0.4rem 1.8rem;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  padding: 0;
}

.events--list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0;
}

.events--grid .events__item {
  display: grid;
  min-height: 200px;
}

.events--list .events__item {
  width: 100%;
  border-bottom: 1px solid var(--color-border);
}

.events--list .events__item:last-child {
  border-bottom: none;
}

.control-button--active {
  background-color: var(--color-accent);
  color: var(--color-background);
  border-color: var(--color-accent);
}
</style>
