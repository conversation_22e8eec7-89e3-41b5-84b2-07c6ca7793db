<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useUsers } from '@/composables/user/useUsers'
import { useProfile } from '@/composables/user/useProfile'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { useToast } from 'vue-toastification'
import { User } from '@/models/User'
import SecuritySettings from '@/components/profile/SecuritySettings.vue'
import ActivityLog from '@/components/profile/ActivityLog.vue'
import AccountManagement from '@/components/profile/AccountManagement.vue'

const { getCurrentUserData, updateUserProfile } = useUsers()
const { uploadProfilePhoto } = useProfile()
const { auth } = useFirebase()
const toast = useToast()

const user = ref<User | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const isEditing = ref(false)
const isSaving = ref(false)
const editedUser = ref<User | null>(null)
const newTag = ref('')
const photoFile = ref<File | null>(null)
const showcaseModel = ref(false)

// Computed property for user's avatar URL
const avatarUrl = computed(() => {
  if (!user.value) return undefined
  return user.value.photoURL || auth.currentUser?.photoURL || '/default-avatar.png'
})

// Format role name for display
function formatRoleName(roleName: string): string {
  switch (roleName.toLowerCase()) {
    case 'admin':
      return 'Administrator'
    case 'artist':
      return 'Artist'
    case 'bandleader':
      return 'Band Leader'
    default:
      return roleName.charAt(0).toUpperCase() + roleName.slice(1)
  }
}

// Wait for Firebase auth to initialize before getting user data
onMounted(async () => {
  try {
    isLoading.value = true
    error.value = ''

    if (auth.currentUser) {
      const userData = await getCurrentUserData()
      if (userData) {
        user.value = userData
        editedUser.value = new User({ ...userData })
      }
    } else {
      error.value = 'Please sign in to view your profile'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    isLoading.value = false
  }
})

async function handleSubmit() {
  try {
    if (!editedUser.value) return

    isSaving.value = true
    error.value = ''

    // Handle photo upload first if there's a new photo
    if (photoFile.value) {
      const photoUrl = await uploadProfilePhoto({
        file: photoFile.value
      })
      if (photoUrl) {
        editedUser.value.photoURL = photoUrl
      }
    }

    await updateUserProfile(editedUser.value)
    user.value = new User({ ...editedUser.value })
    isEditing.value = false
    toast.success('Profile updated successfully')
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to update profile'
    toast.error('Failed to update profile')
  } finally {
    isSaving.value = false
  }
}

function addTag() {
  if (!editedUser.value || !newTag.value.trim()) return
  if (!editedUser.value.tags) {
    editedUser.value.tags = []
  }
  editedUser.value.tags.push(newTag.value.trim())
  newTag.value = ''
}

function removeTag(index: number) {
  if (!editedUser.value?.tags) return
  editedUser.value.tags.splice(index, 1)
}

function startEditing() {
  if (!user.value) return
  editedUser.value = new User({ ...user.value })
  isEditing.value = true
}

function cancelEditing() {
  if (!user.value) return
  editedUser.value = new User({ ...user.value })
  isEditing.value = false
  error.value = ''
  photoFile.value = null
}

function handlePhotoChange(file: File | null) {
  photoFile.value = file
}
</script>

<template>
  <BaseSection v-if="isLoading">
    <div class="loading-message">
      <LoadingSpinner />
      <p>Loading profile...</p>
    </div>
  </BaseSection>

  <div v-else-if="error" class="error-message">
    {{ error }}
  </div>

  <div v-else class="profile-content">
    <!-- Profile Header -->
    <BaseSection class="profile-header">
      <div class="avatar-section">
        <BasePhotoUpload v-if="isEditing" v-model="photoFile" :preview-url="avatarUrl" :aspect-ratio="1"
          @error="error = $event" />
        <img v-else :src="avatarUrl" :alt="user?.username" class="avatar" />
      </div>
      <div class="header-content">
        <h1>{{ user?.fullName }}</h1>
        <div class="roles">
          <BaseBadge v-for="role in user?.roles" :key="role.type" variant="solid" purpose="secondary">
            {{ formatRoleName(role.type) }}
          </BaseBadge>
        </div>
      </div>
      <div class="header-actions">
        <BaseButton v-if="!isEditing" @click="startEditing" type="secondary">
          Edit Profile
        </BaseButton>
        <BaseButton v-else @click="cancelEditing" type="secondary">
          Cancel
        </BaseButton>
      </div>
    </BaseSection>

    <!-- Profile Form -->
    <form v-if="isEditing" @submit.prevent="handleSubmit" class="profile-form">
      <BaseSection title="Basic Information">
        <BaseCard>
          <div class="form-layout">
            <div class="form-row">
              <BaseInput v-model="editedUser!.firstName" label="First Name" required />
              <BaseInput v-model="editedUser!.lastName" label="Last Name" required />
            </div>
            <div class="form-row">
              <BaseInput v-model="editedUser!.username" label="Username" required />
              <BaseInput v-model="editedUser!.email" label="Email" type="email" required disabled />
            </div>
            <div class="form-row">
              <BaseInput v-model="editedUser!.phone" label="Phone" type="tel" />
              <BaseInput v-model="editedUser!.birthday" label="Birthday" type="date" />
            </div>
            <div class="form-row">
              <BaseInput v-model="editedUser!.address" label="Address" type="text" class="full-width" />
            </div>
          </div>
        </BaseCard>
      </BaseSection>

      <BaseSection title="Preferences">
        <div class="preferences-grid">
          <BaseToggle v-model="editedUser!.prefs.isSubscribed" label="Subscribe to notifications" />
          <BaseToggle v-model="editedUser!.prefs.viewAsGrid" label="View as grid" />
        </div>
      </BaseSection>

      <BaseSection title="Tags">
        <div class="tags-section">
          <div class="tags-input">
            <BaseInput v-model="newTag" placeholder="Add a tag" @keyup.enter.prevent="addTag">
              <template #append>
                <BaseButton type="secondary" @click.prevent="addTag" :disabled="!newTag.trim()">
                  Add
                </BaseButton>
              </template>
            </BaseInput>
          </div>
          <div class="tags-list">
            <BaseBadge v-for="(tag, index) in editedUser?.tags" :key="index" variant="solid" purpose="primary"
              @click="removeTag(index)">
              {{ tag }}
            </BaseBadge>
          </div>
        </div>
      </BaseSection>

      <div class="form-actions">
        <BaseButton type="secondary" @click="cancelEditing" :disabled="isSaving">
          Cancel
        </BaseButton>
        <BaseButton type="primary" :loading="isSaving" native-type="submit">
          Save Changes
        </BaseButton>
      </div>
    </form>

    <!-- Profile Display -->
    <div v-else class="profile-display">
      <BaseSection title="Basic Information">
        <div class="info-grid">
          <div class="info-item">
            <label>Username</label>
            <p>{{ user?.username }}</p>
          </div>
          <div class="info-item">
            <label>Email</label>
            <p>{{ user?.email }}</p>
          </div>
          <div class="info-item">
            <label>Phone</label>
            <p>{{ user?.phone || 'Not provided' }}</p>
          </div>
          <div class="info-item">
            <label>Address</label>
            <p>{{ user?.address || 'Not provided' }}</p>
          </div>
          <div class="info-item">
            <label>Birthday</label>
            <p>{{ user?.birthday ? new Date(user.birthday.seconds * 1000).toLocaleDateString() : 'Not provided' }}</p>
          </div>
        </div>
      </BaseSection>

      <BaseSection title="Tags">
        <div class="tags-display">
          <BaseBadge v-for="tag in user?.tags" :key="tag" variant="solid" purpose="primary">
            {{ tag }}
          </BaseBadge>
          <p v-if="!user?.tags?.length" class="no-tags">
            No tags added
          </p>
        </div>
      </BaseSection>

      <!-- Security Settings -->
      <SecuritySettings />

      <!-- Activity Log -->
      <ActivityLog />

      <!-- Account Management -->
      <AccountManagement />
    </div>
  </div>
</template>

<style scoped>
.profile-header {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--space-l);
  align-items: center;
  background: var(--color-surface);
  padding: var(--space-m);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.avatar-section {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--color-border);
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-content {
  h1 {
    margin: 0;
    font-size: var(--step-2);
    color: var(--color-heading);
    font-weight: 600;
  }

  .roles {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2xs);
    margin-top: var(--space-xs);
  }
}

.form-layout {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-m);
}

.form-row .full-width {
  grid-column: 1 / -1;
}

/* Make form rows stack on mobile */
@container (max-width: 500px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-m);
}

.info-item {
  label {
    font-weight: 500;
    color: var(--color-text-soft);
    margin-bottom: var(--space-3xs);
    display: block;
    font-size: var(--step--1);
  }

  p {
    margin: 0;
    color: var(--color-text);
    font-size: var(--step-0);
  }
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2xs);
}

.no-tags {
  color: var(--color-text-muted);
  font-style: italic;
  font-size: var(--step--1);
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-s);
  min-height: 200px;
  color: var(--color-text-soft);
  font-size: var(--step-0);
}

.error-message {
  text-align: center;
  color: var(--color-danger);
  padding: var(--space-l);
  font-size: var(--step-0);
  background: var(--color-danger-soft);
  border-radius: var(--radius);
}

.profile-form,
.profile-display {
  display: flex;
  flex-direction: column;
  gap: var(--space-l);
  margin-top: var(--space-l);
}

.form-actions {
  display: flex;
  gap: var(--space-s);
  justify-content: flex-end;
  margin-top: var(--space-m);
}

.preferences-grid {
  display: grid;
  gap: var(--space-m);
  max-width: 400px;
}

.tags-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2xs);
}

@media (max-width: 768px) {
  .profile-header {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .avatar-section {
    margin: 0 auto;
  }

  .header-content {
    .roles {
      justify-content: center;
    }
  }

  .header-actions {
    margin-top: var(--space-m);
  }
}

/* Add showcase styles at the top */
.toggle-showcase {
  margin-bottom: var(--space-xl);
  background: var(--color-surface);
  padding: var(--space-m);
  border-radius: var(--radius);
}

.toggle-group {
  margin-bottom: var(--space-l);

  h3 {
    color: var(--color-text-soft);
    font-size: var(--step--1);
    margin-bottom: var(--space-s);
  }
}

.toggle-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-m);
  align-items: center;
}
</style>
