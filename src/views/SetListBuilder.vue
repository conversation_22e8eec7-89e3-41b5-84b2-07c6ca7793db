<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useFirestore } from '@/composables/firebase/useFirestore'
import { Plus } from 'lucide-vue-next'
import ActBadgeLogo from '@/components/acts/ActBadgeLogo.vue'
import TextLogo from '@/components/TextLogo.vue'
import { Act } from '@/models/Act'
import type { Song, Set } from '@/types/song'

const props = defineProps({
  defaultAct: {
    type: Object as () => Act | null,
    default: null
  }
})

const actsDb = useFirestore('acts')
const repertoireDb = useFirestore('repertoire')

const repertoire = ref<Song[]>([])
const acts = ref<Act[]>([])
const MAX_SETS = 4

// Initialize with one set
const sets = ref<Set[]>([
  { id: generateId(), songs: [], act: props.defaultAct }
])

const activeSetId = ref<string>(sets.value[0].id)
const showActSelector = ref<string | false>(false)
const draggedSong = ref<Song | null>(null)
const dragSourceSet = ref<string | null>(null)
const dragSourceIndex = ref<number | null>(null)
const isDraggingOver = ref<string | null>(null)

// Computed property to check if we can add more sets
const canAddSet = computed(() => sets.value.length < MAX_SETS)

// Fetch all acts for datalist
async function fetchActs() {
  try {
    const actsData = await actsDb.getAllDocs()
    acts.value = actsData as Act[]
  } catch (error) {
    console.error('Error fetching acts:', error)
  }
}

// Fetch songs for selected act
async function fetchRepertoire(actId: string) {
  if (!actId) return

  try {
    console.log('Fetching repertoire for act:', actId)
    const repertoireData = await repertoireDb.getDocuments({
      where: [['acts', 'array-contains', actId]]
    })
    repertoire.value = (repertoireData as Song[]).sort((a, b) =>
      a.title.localeCompare(b.title)
    )
  } catch (error) {
    console.error('Error fetching repertoire:', error)
    repertoire.value = []
  }
}

// Handle act selection for a set
function onActSelect(setId: string, actId: string) {
  const act = acts.value.find(a => a.id === actId)
  if (act) {
    const set = sets.value.find(s => s.id === setId)
    if (set) {
      set.act = act
      set.songs = [] // Clear songs when act changes
      fetchRepertoire(act.id)
      showActSelector.value = false // Hide selector after selection
    }
  }
}

// Add new set
function addSet() {
  if (canAddSet.value) {
    const newSetId = generateId()
    const lastSet = sets.value[sets.value.length - 1]
    sets.value.push({
      id: newSetId,
      songs: [],
      act: lastSet.act
    })
    activeSetId.value = newSetId
  }
}

// Remove set
function removeSet(setId: string) {
  const index = sets.value.findIndex(s => s.id === setId)
  if (index !== -1) {
    sets.value.splice(index, 1)
    if (activeSetId.value === setId) {
      activeSetId.value = sets.value[0]?.id || ''
    }
  }
}

function handleDragStart(event: DragEvent, song: Song, setId: string | null = null, index: number | null = null) {
  if (!event.dataTransfer) return
  draggedSong.value = song
  dragSourceSet.value = setId
  dragSourceIndex.value = index
  event.dataTransfer.setData('songId', song.id)
  event.dataTransfer.effectAllowed = 'move'
}

function handleDragOver(event: DragEvent, targetId: string) {
  event.preventDefault()
  isDraggingOver.value = targetId
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

function handleDragLeave(event: DragEvent) {
  const target = event.relatedTarget as HTMLElement | null
  if (!target?.closest('.drop-zone-container')) {
    isDraggingOver.value = null
  }
}

function handleDrop(event: DragEvent, setId: string, index: number) {
  event.preventDefault()
  if (!event.dataTransfer) return

  const songId = event.dataTransfer.getData('songId')
  const song = draggedSong.value

  if (!song) return

  // If dragging from a set, remove the song from its original position
  if (dragSourceSet.value && dragSourceIndex.value !== null) {
    const sourceSet = sets.value.find(s => s.id === dragSourceSet.value)
    if (sourceSet) {
      sourceSet.songs.splice(dragSourceIndex.value, 1)

      // Adjust target index if dropping in the same set after the removal point
      if (setId === dragSourceSet.value && index > dragSourceIndex.value) {
        index--
      }
    }
  } else {
    // If dragging from the pool, remove from other sets
    sets.value.forEach(set => {
      set.songs = set.songs.filter(s => s.id !== songId)
    })
  }

  // Add to target set at specific index
  const targetSet = sets.value.find(s => s.id === setId)
  if (targetSet) {
    targetSet.songs.splice(index, 0, song)
  }

  // Reset drag state
  isDraggingOver.value = null
  draggedSong.value = null
  dragSourceSet.value = null
  dragSourceIndex.value = null
}

// Add function to remove song from set
function removeSong(setId: string, index: number) {
  const set = sets.value.find(s => s.id === setId)
  if (set) {
    set.songs.splice(index, 1)
  }
}

const toggleActSelector = (setId: string) => {
  // If clicking the same set's selector, toggle it
  if (showActSelector.value === setId) {
    showActSelector.value = false
  } else {
    // Otherwise, show the selector for this set
    showActSelector.value = setId
  }
}

// Add a click outside handler to close the selector
function onClickOutside(event: MouseEvent) {
  const target = event.target as HTMLElement
  const popups = document.querySelectorAll('.act-selector-popup')
  const badges = document.querySelectorAll('.selected-act-badge')
  const buttons = document.querySelectorAll('.select-act-button')

  // Check if click is outside both popup and badges/buttons
  const isOutside = ![...popups, ...badges, ...buttons].some(el => el.contains(target))

  if (isOutside) {
    showActSelector.value = false
  }
}

// Lifecycle hook
onMounted(() => {
  fetchActs()
  if (props.defaultAct) {
    fetchRepertoire(props.defaultAct.id)
  }

  // Add click outside listener
  document.addEventListener('click', onClickOutside)
})

// Add a function to generate unique IDs
function generateId() {
  return crypto.randomUUID()
}

// Add computed property for current act
const currentAct = computed(() => {
  return sets.value.find(s => s.id === activeSetId.value)?.act
})

// Update the filteredRepertoire computed property
const filteredRepertoire = computed(() => {
  const currentSet = sets.value.find(s => s.id === activeSetId.value)
  const currentActId = currentSet?.act?.id
  if (!currentActId) return []

  // Get all sets that have the same act
  const setsWithSameAct = sets.value.filter(set => {
    const setActId = set.act?.id
    return setActId && setActId === currentActId
  })

  // Get all song IDs used in any set of the current act
  const usedSongIds = new Set(
    setsWithSameAct.flatMap(set =>
      set.songs.map(song => song.id)
    )
  )

  // Return only songs that aren't in any set of the current act
  return repertoire.value.filter(song =>
    !usedSongIds.has(song.id)
  )
})

// Add a ref for the filter
const songFilter = ref('')

// Add a computed property for filtered songs
const filteredAndSearchedRepertoire = computed(() => {
  const filter = songFilter.value.toLowerCase().trim()
  return filteredRepertoire.value.filter(song =>
    filter === '' ||
    song.shortTitle.toLowerCase().includes(filter) ||
    song.artist.toLowerCase().includes(filter)
  )
})

// Add a watcher to update repertoire when active set changes
watch(() => {
  const currentSet = sets.value.find(s => s.id === activeSetId.value)
  const actId = currentSet?.act?.id
  return actId || null
}, (newActId) => {
  if (newActId) {
    fetchRepertoire(newActId)
  } else {
    repertoire.value = []
  }
})
</script>

<template>
  <h1>Set List Builder</h1>

  <div class="builder-container">
    <!-- Available songs -->
    <div class="song-pool">
      <div class="song-pool-header">
        <div class="act-selection">
          <div v-if="currentAct">
            <TextLogo v-if="currentAct.logoUrls?.text" :public-id="currentAct.logoUrls.text" :alt="currentAct.name"
              height="40" class="act-text-logo clickable" @click="toggleActSelector('pool')" />
            <BaseButton v-else class="select-act-button" @click="toggleActSelector('pool')">
              {{ currentAct.name }}
            </BaseButton>
          </div>
          <BaseButton v-else class="select-act-button" @click="toggleActSelector('pool')">
            Select Act
          </BaseButton>

          <!-- Act selector popup -->
          <div v-if="showActSelector === 'pool'" class="act-selector-popup">
            <div class="act-filters">
              <BaseButton v-for="act in acts" :key="act.id" class="act-filter" @click="onActSelect(activeSetId, act.id)"
                :title="act.name">
                <ActBadgeLogo v-if="act.logoUrls?.['badge']" :actId="act.id" size="1.5em" class="act-filter-badge"
                  :alt="act.name" />
                <span v-else>{{ act.name }}</span>
              </BaseButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Only show filter and songs when there's a current act -->
      <template v-if="currentAct">
        <div class="song-filter">
          <input type="text" v-model="songFilter" placeholder="Filter songs..." class="filter-input">
        </div>

        <div class="songs-container" @dragover.prevent>
          <div v-for="song in filteredAndSearchedRepertoire" :key="song.id" class="song-item" draggable="true"
            @dragstart="handleDragStart($event, song)" :title="song.title">
            <div class="song-title">{{ song.shortTitle }}</div>
            <div class="song-artist">{{ song.artist }}</div>
          </div>

          <div v-if="filteredAndSearchedRepertoire.length === 0" class="empty-pool">
            {{ songFilter ? 'No songs match your filter' : 'No more songs available' }}
          </div>
        </div>
      </template>
    </div>

    <!-- Sets Container -->
    <div class="sets-section">
      <!-- Tabs -->
      <div class="set-tabs">
        <BaseButton v-for="(set, index) in sets" :key="set.id" class="tab-button"
          :class="{ active: activeSetId === set.id }" @click="activeSetId = set.id">
          Set {{ index + 1 }}
          <BaseButton v-if="sets.length > 1" class="remove-set" @click.stop="removeSet(set.id)" title="Remove Set">
            ×
          </BaseButton>
        </BaseButton>
        <BaseButton v-if="canAddSet" class="tab-button add-set" @click="addSet" title="Add Set">
          <Plus />
          <span>Add Set</span>
        </BaseButton>
      </div>

      <!-- Active Set Content -->
      <div class="set-box">
        <div v-for="set in sets" :key="set.id" v-show="activeSetId === set.id">
          <!-- Act selector for set -->
          <div class="act-selection">
            <div v-if="set.act">
              <ActBadgeLogo v-if="set.act.logoUrls?.badge" :actId="set.act.id" class="selected-act-badge"
                @click="toggleActSelector(set.id)" size="1.5em" :alt="set.act.name" />

              <BaseButton v-else class="select-act-button" @click="toggleActSelector(set.id)">
                {{ set.act.name }}
              </BaseButton>
            </div>
            <BaseButton v-else class="select-act-button" @click="toggleActSelector(set.id)">
              Select Act
            </BaseButton>

            <!-- Act selector popup -->
            <div v-if="showActSelector === set.id" class="act-selector-popup">
              <div class="act-filters">
                <BaseButton v-for="act in acts" :key="act.id" class="act-filter" @click="onActSelect(set.id, act.id)"
                  :title="act.name">
                  <ActBadgeLogo v-if="act.logoUrls?.['badge']" :actId="act.id" size="1.5em" class="act-filter-badge"
                    :alt="act.name" />
                  <span v-else>{{ act.name }}</span>
                </BaseButton>
              </div>
            </div>
          </div>

          <!-- Add drop zone container -->
          <div class="drop-zone-container" v-if="currentAct">
            <!-- First drop zone -->
            <div v-if="set.songs.length === 0" class="drop-zone empty-set"
              :class="{ 'drop-zone--active': isDraggingOver === `${set.id}-0` }"
              @dragover.prevent="handleDragOver($event, `${set.id}-0`)" @dragleave="handleDragLeave"
              @drop="handleDrop($event, set.id, 0)">
              Drag songs here to build your set
            </div>

            <!-- Songs with drop zones between them -->
            <template v-else>
              <!-- Initial drop zone -->
              <div class="drop-zone" :class="{ 'drop-zone--active': isDraggingOver === `${set.id}-0` }"
                @dragover.prevent="handleDragOver($event, `${set.id}-0`)" @dragleave="handleDragLeave"
                @drop="handleDrop($event, set.id, 0)" />

              <template v-for="(song, index) in set.songs" :key="song.id">
                <!-- Song item with remove button -->
                <div class="song-item" draggable="true" @dragstart="handleDragStart($event, song, set.id, index)"
                  :title="song.title">
                  <div class="song-info">
                    <div class="song-title">{{ song.shortTitle }}</div>
                    <div class="song-artist">{{ song.artist }}</div>
                  </div>
                  <BaseButton class="remove-song" @click="removeSong(set.id, index)" title="Remove song">
                    ×
                  </BaseButton>
                </div>

                <!-- Drop zone after each song -->
                <div class="drop-zone" :class="{ 'drop-zone--active': isDraggingOver === `${set.id}-${index + 1}` }"
                  @dragover.prevent="handleDragOver($event, `${set.id}-${index + 1}`)" @dragleave="handleDragLeave"
                  @drop="handleDrop($event, set.id, index + 1)" />
              </template>
            </template>
          </div>
          <div v-else class="no-act-message">
            Select an act to start building your set
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.builder-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  margin-top: 2rem;
  flex: 1;
  min-height: 0;
}

.song-pool {
  background: var(--color-background-soft);
  padding: 1rem;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sets-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: 0;
}

.set-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 2px solid var(--color-border);
  padding-bottom: 0.5rem;
}

.tab-button {
  padding: 0.5rem 1.5rem;
  border: 2px solid var(--color-border);
  border-radius: 8px 8px 0 0;
  background: var(--color-background);
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-light);
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-background-soft);
    color: var(--color-text);
  }

  &.active {
    background: var(--color-background-soft);
    border-bottom-color: var(--color-background-soft);
    margin-bottom: -2px;
    color: var(--color-accent);
    font-weight: 600;
  }

  &.add-set {
    padding: 0.5rem 1rem;
    color: var(--color-accent);
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:hover {
      background: var(--color-background-soft);
      transform: scale(1.05);
    }
  }
}

.remove-set {
  background: none;
  border: none;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  opacity: 0.6;
  color: var(--color-text);

  &:hover {
    opacity: 1;
    background: var(--color-background-mute);
    color: var(--color-error);
  }
}

.set-box {
  background: var(--color-background-soft);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}

.set-box>div {
  display: flex;
  flex-direction: column;
}

.song-item {
  background: var(--color-background-mute);
  padding: 0.5rem 0.75rem;
  margin: 0.25rem 0;
  border-radius: 4px;
  cursor: move;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: var(--color-background);
    transform: translateY(-1px);
  }

  &:active {
    cursor: grabbing;
    transform: scale(0.98);
  }
}

.song-info {
  flex: 1;
  min-width: 0;
}

.remove-song {
  background: none;
  border: none;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  opacity: 0;
  color: var(--color-text);
  transition: all var(--transition-in);

  &:hover {
    opacity: 1;
    background: var(--color-background-mute);
    color: var(--color-error);
  }
}

.song-item:hover .remove-song {
  opacity: 0.6;
}

.empty-set {
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  color: var(--color-text-light);
  transition: all var(--transition-in);

  &.drop-zone--active {
    border-color: var(--color-accent);
    background: var(--color-background-mute);
    color: var(--color-accent);
  }
}

.empty-pool {
  text-align: center;
  padding: 2rem;
  color: var(--color-text-light);
  font-style: italic;
}

.act-selection {
  position: relative;
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.selected-act-badge {
  cursor: pointer;
  transition: transform var(--transition-in);
  border: 2px solid var(--color-border);
  border-radius: 50%;
  padding: 0.25rem;

  &:hover {
    transform: scale(1.05);
    border-color: var(--color-accent);
  }
}

.select-act-button {
  padding: 0.5rem 1rem;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  font-size: 0.9rem;
  transition: all var(--transition-in);

  &:hover {
    border-color: var(--color-accent);
    color: var(--color-accent);
    transform: translateY(-1px);
  }
}

.act-selector-popup {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.act-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.act-filter {
  padding: 0.25rem;
  border: 2px solid var(--color-border);
  border-radius: 50%;
  background: var(--color-background);
  cursor: pointer;
  transition: all var(--transition-in);

  &:hover {
    border-color: var(--color-accent);
    transform: translateY(-2px);
  }
}

.act-filter-badge {
  display: block;
  transition: filter var(--transition-in);
}

.act-filter:hover .act-filter-badge {
  filter: brightness(1.1);
}

.song-pool-header {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.song-pool-header .act-selection {
  position: relative;
  display: flex;
  justify-content: center;
}

.act-text-logo {
  max-width: 250px;
  height: auto;
  margin-inline: auto;
  display: block;
  transition: filter var(--transition-in);
}

.act-text-logo.clickable {
  cursor: pointer;
  transition: transform var(--transition-in);

  &:hover {
    transform: scale(1.05);
  }
}

.song-filter {
  flex-shrink: 0;
  /* Prevent filter from shrinking */
  margin-bottom: 1rem;
}

.filter-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  font: inherit;

  &:focus {
    outline: none;
    border-color: var(--color-accent);
  }

  &::placeholder {
    color: var(--color-text-light);
  }
}

.songs-container {
  flex-grow: 1;
  /* Take remaining space */
  overflow-y: auto;
  /* Enable vertical scrolling */
  padding-right: 0.5rem;
  /* Space for scrollbar */

  /* Customize scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-background);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: 4px;

    &:hover {
      background: var(--color-text-light);
    }
  }
}

.drop-zone-container {
  padding: 0.5rem;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.drop-zone {
  height: 4px;
  margin: 4px 0;
  border-radius: 2px;
  transition: all var(--transition-in);
  background: transparent;

  &--active {
    background: var(--color-accent);
    height: 8px;
  }
}

.empty-set {
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  color: var(--color-text-light);
  transition: all var(--transition-in);

  &.drop-zone--active {
    border-color: var(--color-accent);
    background: var(--color-background-mute);
    color: var(--color-accent);
  }
}

.no-act-message {
  text-align: center;
  padding: 2rem;
  color: var(--color-text-light);
  font-style: italic;
  background: var(--color-background-mute);
  border-radius: 8px;
}
</style>
