import { ref } from 'vue'
import { useFirebase } from '@/composables/firebase/useFirebase'
import {
  collection,
  addDoc,
  query,
  orderBy,
  limit,
  getDocs,
  where,
  Timestamp,
  type QueryConstraint,
} from 'firebase/firestore'
import type { UserActivity } from '@/types/user'

export const ACTIVITY_TYPES = {
  // Auth Events
  LOGIN: 'login',
  LOGOUT: 'logout',
  PASSWORD_RESET_REQUESTED: 'password_reset_requested',
  PASSWORD_RESET_COMPLETED: 'password_reset_completed',
  EMAIL_VERIFIED: 'email_verified',

  // Profile Events
  PROFILE_UPDATED: 'profile_updated',
  PHOTO_UPDATED: 'photo_updated',
  EMAIL_UPDATED: 'email_updated',

  // Security Events
  PASSWORD_CHANGED: 'password_changed',
  TWO_FACTOR_ENABLED: 'two_factor_enabled',
  TWO_FACTOR_DISABLED: 'two_factor_disabled',
  SECURITY_SETTINGS_UPDATED: 'security_settings_updated',

  // Data Events
  DATA_EXPORTED: 'data_exported',

  // Account Events
  ACCOUNT_CREATED: 'account_created',
  ACCOUNT_DELETED: 'account_deleted',

  // Preference Events
  PREFERENCES_UPDATED: 'preferences_updated',

  // Role Events
  ROLE_ADDED: 'role_added',
  ROLE_REMOVED: 'role_removed',
} as const

export type ActivityType = keyof typeof ACTIVITY_TYPES

export interface FetchOptions {
  limit?: number
  startDate?: Date
  endDate?: Date
  activityType?: ActivityType
}

export function useActivityLog() {
  const { db, auth } = useFirebase()
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  /**
   * Log a new activity
   */
  async function logActivity(
    action: ActivityType,
    details?: Record<string, any>,
  ): Promise<void> {
    if (!auth.currentUser) return

    try {
      const activity: UserActivity = {
        timestamp: Timestamp.now(),
        action,
        // Only include details if they are provided
        ...(details ? { details } : {}),
        ip: window.clientInformation?.userAgent ?? null,
        userAgent: navigator.userAgent,
      }

      const userRef = collection(db, 'users', auth.currentUser.uid, 'activity')
      await addDoc(userRef, activity)
    } catch (err) {
      console.error('Failed to log activity:', err)
      error.value =
        err instanceof Error ? err.message : 'Failed to log activity'
      throw err
    }
  }

  /**
   * Fetch user activities with filtering options
   */
  async function fetchActivities(
    options: FetchOptions = {},
  ): Promise<Array<UserActivity>> {
    if (!auth.currentUser) return []

    try {
      isLoading.value = true
      error.value = null

      const constraints: QueryConstraint[] = [orderBy('timestamp', 'desc')]

      // Add limit if specified
      if (options.limit) {
        constraints.push(limit(options.limit))
      }

      // Add date range if specified
      if (options.startDate) {
        constraints.push(
          where('timestamp', '>=', Timestamp.fromDate(options.startDate)),
        )
      }
      if (options.endDate) {
        constraints.push(
          where('timestamp', '<=', Timestamp.fromDate(options.endDate)),
        )
      }

      // Add activity type filter if specified
      if (options.activityType) {
        constraints.push(where('action', '==', options.activityType))
      }

      const userRef = collection(db, 'users', auth.currentUser.uid, 'activity')
      const q = query(userRef, ...constraints)
      const snapshot = await getDocs(q)

      return snapshot.docs.map(doc => {
        const data = doc.data()
        return {
          id: doc.id,
          timestamp: data.timestamp as Timestamp,
          action: data.action as string,
          details: data.details as Record<string, any> | undefined,
          ip: data.ip as string | undefined,
          userAgent: data.userAgent as string | undefined,
        } satisfies UserActivity
      })
    } catch (err) {
      console.error('Error fetching activities:', err)
      error.value =
        err instanceof Error ? err.message : 'Failed to fetch activities'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get a human-readable label for an activity type
   */
  function getActivityLabel(action: string): string {
    const labels: Record<ActivityType, string> = {
      LOGIN: 'Logged in',
      LOGOUT: 'Logged out',
      PASSWORD_RESET_REQUESTED: 'Password reset requested',
      PASSWORD_RESET_COMPLETED: 'Password reset completed',
      EMAIL_VERIFIED: 'Email verified',
      PROFILE_UPDATED: 'Profile updated',
      PHOTO_UPDATED: 'Profile photo updated',
      EMAIL_UPDATED: 'Email address updated',
      PASSWORD_CHANGED: 'Password changed',
      TWO_FACTOR_ENABLED: 'Two-factor authentication enabled',
      TWO_FACTOR_DISABLED: 'Two-factor authentication disabled',
      SECURITY_SETTINGS_UPDATED: 'Security settings updated',
      DATA_EXPORTED: 'Account data exported',
      ACCOUNT_CREATED: 'Account created',
      ACCOUNT_DELETED: 'Account deleted',
      PREFERENCES_UPDATED: 'Preferences updated',
      ROLE_ADDED: 'Role added',
      ROLE_REMOVED: 'Role removed',
    }

    return labels[action as ActivityType] || action
  }

  return {
    isLoading,
    error,
    logActivity,
    fetchActivities,
    getActivityLabel,
    ACTIVITY_TYPES,
  }
}
