import { ref, watchEffect } from 'vue'
import { doc, getDoc } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'

export function useUserPrefs() {
  const { db, currentUser } = useFirebase()
  const viewAsGrid = ref(true)
  const isLoadingPrefs = ref(true)
  const error = ref<string | null>(null)

  const fetchPrefs = async () => {
    try {
      isLoadingPrefs.value = true
      error.value = null

      if (currentUser.value) {
        const prefsDoc = await getDoc(doc(db, 'users', currentUser.value.uid))
        if (prefsDoc.exists()) {
          viewAsGrid.value = prefsDoc.data()?.prefs?.viewAsGrid ?? true
        }
      }
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
      } else {
        error.value = 'An unknown error occurred'
      }
      console.error('Error loading preferences:', e)
    } finally {
      isLoadingPrefs.value = false
    }
  }

  // Watch for auth changes
  watchEffect(() => {
    if (currentUser.value) {
      fetchPrefs()
    }
  })

  return {
    viewAsGrid,
    isLoadingPrefs,
    error,
  }
}
