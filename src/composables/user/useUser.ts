import { ref } from 'vue'
import { doc, getDoc } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import type { UserRole } from '@/types/user'

interface UserPreferences {
  isSubscribed: boolean
  // Add other preference fields as needed
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  roles?: UserRole[]
  isActive?: boolean
  lastLogin?: string
  createdAt?: string
  prefs: UserPreferences
}

export function useUser() {
  const { db } = useFirebase()
  const user = ref<User | null>(null)
  const isLoading = ref(true)
  const error = ref<string | null>(null)

  /**
   * Fetches user details from Firestore
   * @param userId - The Firebase Auth UID of the user
   * @returns Promise<User | null> - The user object or null if not found
   */
  async function fetchUserDetails(userId: string): Promise<User | null> {
    isLoading.value = true
    error.value = null

    try {
      const userDoc = await getDoc(doc(db, 'users', userId))

      if (!userDoc.exists()) {
        error.value = 'User not found'
        return null
      }

      const userData = userDoc.data()
      user.value = {
        id: userDoc.id,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        roles: userData.roles || [],
        isActive: userData.isActive ?? true,
        lastLogin: userData.lastLogin,
        createdAt: userData.createdAt,
        prefs: userData.prefs || { isSubscribed: false },
      }

      return user.value
    } catch (err) {
      console.error('Error fetching user details:', err)
      error.value = 'Failed to fetch user details'
      return null
    } finally {
      isLoading.value = false
    }
  }

  return {
    user,
    isLoading,
    error,
    fetchUserDetails,
  }
}
