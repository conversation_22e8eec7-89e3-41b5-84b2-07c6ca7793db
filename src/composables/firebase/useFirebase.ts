import { ref, computed } from 'vue'
import { initializeApp } from 'firebase/app'
import {
  getAuth,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signOut,
  type User,
} from 'firebase/auth'
import { getFirestore, doc, getDoc } from 'firebase/firestore'
import { getAnalytics } from 'firebase/analytics'
import { getStorage } from 'firebase/storage'

const firebaseConfig = {
  apiKey: 'AIzaSyC5xqKHMlRsQ8crdiLz5GlUT9JuOpqD5GM',
  authDomain: 'daves-roy-orbison.firebaseapp.com',
  databaseURL: 'https://daves-roy-orbison.firebaseio.com',
  projectId: 'daves-roy-orbison',
  storageBucket: 'daves-roy-orbison.appspot.com',
  messagingSenderId: '699866740054',
  appId: '1:699866740054:web:502c0fd807013f77ea52f7',
  measurementId: 'G-MZZ8VSL28R',
}

// Initialize Firebase once
const firebaseApp = initializeApp(firebaseConfig)
const auth = getAuth(firebaseApp)
const db = getFirestore(firebaseApp)
const analytics = getAnalytics(firebaseApp)
const storage = getStorage(firebaseApp)

interface UserData extends User {
  isAdmin?: boolean
  [key: string]: any
}

export function useFirebase() {
  const currentUser = ref<UserData | null>(null)
  const isLoading = ref(true)
  const isAdmin = ref(false)
  const error = ref<string | null>(null)

  const currentUserFirstName = computed(() => {
    if (!currentUser.value?.displayName) return null
    return currentUser.value.displayName.split(' ')[0]
  })

  const login = async (email: string, password: string) => {
    try {
      error.value = null
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password,
      )
      const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid))
      isAdmin.value = userDoc.data()?.isAdmin || false
      return userCredential.user
    } catch (e) {
      console.error('Login error:', e)
      error.value = 'Invalid email or password'
      return null
    }
  }

  const logout = async () => {
    try {
      await signOut(auth)
      currentUser.value = null
      isAdmin.value = false
      return true
    } catch (e) {
      console.error('Logout error:', e)
      error.value = 'Failed to logout'
      return false
    }
  }

  // Setup auth state listener
  onAuthStateChanged(auth, async user => {
    isLoading.value = true
    try {
      if (user) {
        const userDoc = await getDoc(doc(db, 'users', user.uid))
        currentUser.value = {
          ...user,
          ...userDoc.data(),
        } as UserData
        const userTokens = await user.getIdTokenResult()
        isAdmin.value = Boolean(userTokens.claims?.admin)
      } else {
        currentUser.value = null
        isAdmin.value = false
      }
    } catch (e) {
      console.error('Auth state change error:', e)
      error.value = 'Authentication error'
    } finally {
      isLoading.value = false
    }
  })

  return {
    firebaseApp,
    auth,
    db,
    analytics,
    storage,
    currentUser,
    currentUserFirstName,
    isLoading,
    isAdmin,
    error,
    login,
    logout,
  }
}
