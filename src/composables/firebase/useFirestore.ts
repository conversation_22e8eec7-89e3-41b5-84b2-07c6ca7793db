import { ref } from 'vue'
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp,
  addDoc,
  type DocumentData,
  type Query,
  type CollectionReference,
  type QueryConstraint,
  type OrderByDirection,
  type DocumentReference,
  type QueryDocumentSnapshot,
  type Unsubscribe,
  type WhereFilterOp,
} from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { timestampToDate } from '@/firebase/utils'
import { useMessageStore, type DialogType } from '@/stores/messageStore'

interface QueryOptions {
  where?: [string, WhereFilterOp, any][]
  orderBy?: [string, OrderByDirection?][]
}

interface DocumentWithId extends DocumentData {
  id: string
}

interface ConfirmOptions {
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
  type?: DialogType
  onConfirm?: () => void
  onCancel?: () => void
}

const { db } = useFirebase()

export function useFirestore(collectionName: string) {
  const documents = ref<DocumentWithId[]>([])
  const document = ref<DocumentWithId | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const messageStore = useMessageStore()

  // Helper function to sanitize strings for IDs
  const sanitizeForId = (str: string): string => {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  // Generic ID generator
  const generateId = (data: DocumentData): string | null => {
    const idFormats: Record<string, { parts: (() => string | undefined)[] }> = {
      events: {
        parts: [
          () => data.when?.toDate().toISOString().split('T')[0],
          () => data.venue,
          () => (Array.isArray(data.acts) ? data.acts.join('-') : data.acts),
        ],
      },
      venues: {
        parts: [() => data.name, () => data.address?.town || ''],
      },
      acts: {
        parts: [() => data.name],
      },
      artists: {
        parts: [() => data.firstName, () => data.lastName],
      },
      shows: {
        parts: [() => data.act, () => data.name],
      },
      repertoire: {
        parts: [() => data.artist, () => data.title],
      },
    }

    const format = idFormats[collectionName]
    if (!format) return null

    try {
      const parts = format.parts.map(partFn => {
        const part = partFn()
        if (!part && part !== '') {
          throw new Error(
            `Missing required data for ID generation in ${collectionName}`,
          )
        }
        return sanitizeForId(String(part))
      })

      return parts.filter(Boolean).join('-')
    } catch (e) {
      console.error(`Error generating ID for ${collectionName}:`, e)
      return null
    }
  }

  // Add a document
  const addDocument = async (data: DocumentData): Promise<DocumentWithId> => {
    try {
      let docRef: DocumentReference
      const customId = generateId(data)

      if (customId) {
        // Use custom ID for specified collections
        docRef = doc(db, collectionName, customId)
        await setDoc(docRef, {
          ...data,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        })
      } else {
        // Use auto-generated ID for other collections
        const collectionRef = collection(db, collectionName)
        docRef = await addDoc(collectionRef, {
          ...data,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        })
      }

      // Get the newly created document
      const newDoc = await getDoc(docRef)
      const docData = newDoc.data()
      if (!docData) throw new Error('Document data is undefined')

      return {
        id: newDoc.id,
        ...docData,
      }
    } catch (e) {
      console.error(`Error adding ${collectionName} document:`, e)
      error.value = `Failed to add ${collectionName}`
      throw e
    }
  }

  // Get a single document
  const getDocument = async (id: string): Promise<DocumentWithId | null> => {
    try {
      isLoading.value = true
      error.value = null

      const docRef = doc(db, collectionName, id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()
        document.value = {
          id: docSnap.id,
          ...data,
          // Convert any timestamps to dates
          ...Object.fromEntries(
            Object.entries(data)
              .filter(([, value]) => value instanceof Timestamp)
              .map(([key, value]) => [
                key,
                timestampToDate(value as Timestamp),
              ]),
          ),
        }
        return document.value
      }
      document.value = null
      return null
    } catch (e) {
      console.error(`Error fetching ${collectionName} document:`, e)
      error.value = `Failed to load ${collectionName}`
      return null
    } finally {
      isLoading.value = false
    }
  }

  // Update a document
  const updateDocument = async (
    id: string,
    data: Partial<DocumentData>,
  ): Promise<boolean> => {
    try {
      const docRef = doc(db, collectionName, id)
      await updateDoc(docRef, {
        ...data,
        updatedAt: Timestamp.now(),
      })
      return true
    } catch (e) {
      console.error(`Error updating ${collectionName} document:`, e)
      error.value = `Failed to update ${collectionName}`
      return false
    }
  }

  // Delete a document with confirmation
  const deleteDocument = async (
    id: string,
    confirmOptions: ConfirmOptions = {},
  ): Promise<boolean> => {
    try {
      const confirmed = await new Promise<boolean>(resolve => {
        messageStore.showDialog({
          title: 'Delete Confirmation',
          message: `Are you sure you want to delete this ${collectionName.slice(0, -1)}?`,
          confirmText: 'Delete',
          cancelText: 'Cancel',
          type: 'confirm',
          onConfirm: () => resolve(true),
          onCancel: () => resolve(false),
          ...confirmOptions,
        })
      })

      if (!confirmed) {
        return false
      }

      // Proceed with deletion
      await deleteDoc(doc(db, collectionName, id))
      return true
    } catch (e) {
      console.error(`Error deleting ${collectionName} document:`, e)
      error.value = `Failed to delete ${collectionName}`
      return false
    }
  }

  // Get all documents with optional query
  const getDocuments = async (
    queryOptions: QueryOptions = {},
  ): Promise<DocumentWithId[]> => {
    try {
      isLoading.value = true
      error.value = null

      let q: Query | CollectionReference = collection(db, collectionName)

      // Build query if options provided
      if (Object.keys(queryOptions).length > 0) {
        const constraints: QueryConstraint[] = []

        if (queryOptions.where) {
          queryOptions.where.forEach(([field, operator, value]) => {
            constraints.push(where(field, operator, value))
          })
        }

        if (queryOptions.orderBy) {
          queryOptions.orderBy.forEach(([field, direction = 'asc']) => {
            constraints.push(orderBy(field, direction))
          })
        }

        q = query(q, ...constraints)
      }

      const snapshot = await getDocs(q)
      documents.value = snapshot.docs.map((doc: QueryDocumentSnapshot) => {
        const data = doc.data()
        return {
          id: doc.id,
          ...data,
          // Convert any timestamps to dates
          ...Object.fromEntries(
            Object.entries(data)
              .filter(([, value]) => value instanceof Timestamp)
              .map(([key, value]) => [
                key,
                timestampToDate(value as Timestamp),
              ]),
          ),
        }
      })

      return documents.value
    } catch (e) {
      console.error(`Error fetching ${collectionName} documents:`, e)
      error.value = `Failed to load ${collectionName}`
      return []
    } finally {
      isLoading.value = false
    }
  }

  // Subscribe to real-time updates
  const subscribe = (
    callback?: (docs: DocumentWithId[]) => void,
    queryOptions: QueryOptions = {},
  ): Unsubscribe => {
    let q: Query | CollectionReference = collection(db, collectionName)

    if (Object.keys(queryOptions).length > 0) {
      const constraints: QueryConstraint[] = []

      if (queryOptions.where) {
        queryOptions.where.forEach(([field, operator, value]) => {
          constraints.push(where(field, operator, value))
        })
      }

      if (queryOptions.orderBy) {
        queryOptions.orderBy.forEach(([field, direction = 'asc']) => {
          constraints.push(orderBy(field, direction))
        })
      }

      q = query(q, ...constraints)
    }

    return onSnapshot(q, snapshot => {
      documents.value = snapshot.docs.map((doc: QueryDocumentSnapshot) => {
        const data = doc.data()
        return {
          id: doc.id,
          ...data,
          // Convert any timestamps to dates
          ...Object.fromEntries(
            Object.entries(data)
              .filter(([, value]) => value instanceof Timestamp)
              .map(([key, value]) => [
                key,
                timestampToDate(value as Timestamp),
              ]),
          ),
        }
      })
      if (callback) callback(documents.value)
    })
  }

  // Get all documents without any query conditions
  const getAllDocs = async (): Promise<DocumentWithId[]> => {
    try {
      isLoading.value = true
      error.value = null

      const snapshot = await getDocs(collection(db, collectionName))
      return snapshot.docs.map((doc: QueryDocumentSnapshot) => {
        const data = doc.data()
        return {
          id: doc.id,
          ...data,
          // Convert any timestamps to dates
          ...Object.fromEntries(
            Object.entries(data)
              .filter(([, value]) => value instanceof Timestamp)
              .map(([key, value]) => [
                key,
                timestampToDate(value as Timestamp),
              ]),
          ),
        }
      })
    } catch (e) {
      console.error(`Error fetching all ${collectionName} documents:`, e)
      error.value = `Failed to load ${collectionName}`
      return []
    } finally {
      isLoading.value = false
    }
  }

  return {
    documents,
    document,
    isLoading,
    error,
    addDocument,
    getDocument,
    updateDocument,
    deleteDocument,
    getDocuments,
    subscribe,
    getAllDocs,
  }
}
