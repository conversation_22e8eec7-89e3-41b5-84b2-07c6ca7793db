import { ref, computed } from 'vue'
import type { Ref, ComputedRef } from 'vue'
import {
  signInWithEmailAndPassword,
  type User as FirebaseUser,
} from 'firebase/auth'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { navigationService } from '@/services/navigation'
import { useActivityLog } from '@/composables/user/useActivityLog'
import type { ActivityType } from '@/composables/user/useActivityLog'

const { auth: firebaseAuth } = useFirebase()
const { logActivity } = useActivityLog()

interface User extends FirebaseUser {
  isAdmin: boolean
}

const user: Ref<User | null> = ref(null)
const isLoading: Ref<boolean> = ref(true)
const error: Ref<string | null> = ref(null)

export function useAuth() {
  const isAuthenticated: ComputedRef<boolean> = computed(() => !!user.value)
  const isAdmin: ComputedRef<boolean> = computed(
    () => user.value?.isAdmin || false,
  )

  async function checkAdminStatus(user: FirebaseUser): Promise<boolean> {
    if (!user) return false
    const idTokenResult = await user.getIdTokenResult()
    return idTokenResult.claims.admin === true
  }

  async function login(email: string, password: string): Promise<boolean> {
    try {
      isLoading.value = true
      error.value = null

      const userCredential = await signInWithEmailAndPassword(
        firebaseAuth,
        email,
        password,
      )
      const isUserAdmin = await checkAdminStatus(userCredential.user)

      if (!isUserAdmin) {
        await firebaseAuth.signOut()
        error.value = 'Access denied. Admin privileges required.'
        return false
      }

      user.value = {
        ...userCredential.user,
        isAdmin: isUserAdmin,
      }

      // Log successful login
      await logActivity('LOGIN' as ActivityType)

      return true
    } catch (e) {
      error.value = 'Invalid login credentials'
      console.error('Login error:', e)
      return false
    } finally {
      isLoading.value = false
    }
  }

  async function logout(): Promise<void> {
    try {
      // Log logout before signing out
      await logActivity('LOGOUT')
      await firebaseAuth.signOut()
      user.value = null
      navigationService.navigate('/login')
    } catch (e) {
      console.error('Logout error:', e)
      // Still try to sign out even if activity logging fails
      await firebaseAuth.signOut()
      user.value = null
      navigationService.navigate('/login')
    }
  }

  // Watch auth state changes
  firebaseAuth.onAuthStateChanged(async (firebaseUser: FirebaseUser | null) => {
    isLoading.value = true
    try {
      if (firebaseUser) {
        const isUserAdmin = await checkAdminStatus(firebaseUser)
        if (!isUserAdmin) {
          await firebaseAuth.signOut()
          user.value = null
          navigationService.navigate('/login')
          return
        }
        user.value = {
          ...firebaseUser,
          isAdmin: isUserAdmin,
        }
      } else {
        user.value = null
        // Redirect to login if on a protected route
        const currentRoute = navigationService.getCurrentRoute()
        if (currentRoute?.meta?.requiresAuth) {
          navigationService.navigate('/login')
        }
      }
    } catch (e) {
      console.error('Auth state change error:', e)
      user.value = null
      navigationService.navigate('/login')
    } finally {
      isLoading.value = false
    }
  })

  return {
    user,
    isLoading,
    error,
    isAuthenticated,
    isAdmin,
    login,
    logout,
  }
}
