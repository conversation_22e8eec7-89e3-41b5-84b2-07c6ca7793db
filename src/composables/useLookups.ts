import { ref, onMounted } from 'vue'
import {
  collection,
  onSnapshot,
  query,
  type QueryDocumentSnapshot,
  type Unsubscribe,
} from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'

interface VenueLookup {
  id: string
  name: string
  address: string
}

interface ActLookup {
  id: string
  name: string
  displayName: string
}

interface VenueLookupMap {
  [key: string]: VenueLookup
}

interface ActLookupMap {
  [key: string]: ActLookup
}

export function useLookups() {
  const { db } = useFirebase()
  const venuesLookup = ref<VenueLookupMap>({})
  const actsLookup = ref<ActLookupMap>({})
  const isLoadingLookups = ref(true)
  const error = ref<string | null>(null)
  let unsubscribeVenues: Unsubscribe | null = null
  let unsubscribeActs: Unsubscribe | null = null

  const subscribeToLookups = () => {
    try {
      // Subscribe to venues
      const venuesQuery = query(collection(db, 'venues'))
      unsubscribeVenues = onSnapshot(venuesQuery, snapshot => {
        const venues: VenueLookupMap = {}
        snapshot.forEach((doc: QueryDocumentSnapshot) => {
          const data = doc.data()
          venues[doc.id] = {
            id: doc.id,
            name: data.name,
            address: data.address,
          }
        })
        venuesLookup.value = venues
      })

      // Subscribe to acts
      const actsQuery = query(collection(db, 'acts'))
      unsubscribeActs = onSnapshot(actsQuery, snapshot => {
        const acts: ActLookupMap = {}
        snapshot.forEach((doc: QueryDocumentSnapshot) => {
          const data = doc.data()
          acts[doc.id] = {
            id: doc.id,
            name: data.name,
            displayName: data.displayName,
          }
        })
        actsLookup.value = acts
      })

      isLoadingLookups.value = false
    } catch (e) {
      console.error('Error setting up lookups:', e)
      error.value = 'Failed to load lookups'
      isLoadingLookups.value = false
    }
  }

  // Cleanup function
  const cleanup = () => {
    if (unsubscribeVenues) unsubscribeVenues()
    if (unsubscribeActs) unsubscribeActs()
  }

  onMounted(() => {
    subscribeToLookups()
    return cleanup
  })

  return {
    venuesLookup,
    actsLookup,
    isLoadingLookups,
    error,
  }
}
