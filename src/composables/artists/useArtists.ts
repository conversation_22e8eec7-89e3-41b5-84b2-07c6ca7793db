import { ref } from 'vue'
import type { Ref } from 'vue'
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  updateDoc,
  onSnapshot,
  serverTimestamp,
  type Unsubscribe,
} from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { Artist } from '@/models/Artist'

export function useArtists() {
  const { db } = useFirebase()
  const artists: Ref<Artist[]> = ref([])
  const isLoadingArtists = ref(true)
  const error: Ref<string | null> = ref(null)
  let unsubscribe: Unsubscribe | null = null

  // Get artists that match the user's email
  const getArtistsByEmail = async (email: string) => {
    try {
      const q = query(collection(db, 'artists'), where('email', '==', email))
      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => Artist.fromFirestore(doc))
    } catch (err: unknown) {
      console.error('Error getting artists by email:', err)
      if (err instanceof Error) {
        error.value = err.message
      }
      return []
    }
  }

  // Get a single artist by ID
  const getArtist = async (artistId: string) => {
    try {
      const artistDoc = await getDoc(doc(db, 'artists', artistId))
      if (!artistDoc.exists()) return null
      return Artist.fromFirestore(artistDoc)
    } catch (err: unknown) {
      console.error('Error getting artist:', err)
      if (err instanceof Error) {
        error.value = err.message
      }
      return null
    }
  }

  // Update artist with user link
  const linkArtistToUser = async (artistId: string, userId: string) => {
    try {
      const artistRef = doc(db, 'artists', artistId)
      await updateDoc(artistRef, {
        userId,
        updatedAt: serverTimestamp(),
      })
      return true
    } catch (err: unknown) {
      console.error('Error linking artist to user:', err)
      if (err instanceof Error) {
        error.value = err.message
      }
      return false
    }
  }

  const subscribeToArtists = () => {
    try {
      isLoadingArtists.value = true
      error.value = null

      unsubscribe = onSnapshot(
        collection(db, 'artists'),
        snapshot => {
          artists.value = snapshot.docs.map(doc => Artist.fromFirestore(doc))
          isLoadingArtists.value = false
        },
        err => {
          if (err instanceof Error) {
            error.value = err.message
            console.error('Error subscribing to artists:', err)
          }
          isLoadingArtists.value = false
        },
      )
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error setting up artist subscription:', e)
      }
      isLoadingArtists.value = false
    }
  }

  // Start subscription
  subscribeToArtists()

  // Cleanup function
  const cleanup = () => {
    if (unsubscribe) {
      unsubscribe()
    }
  }

  return {
    artists,
    isLoadingArtists,
    error,
    cleanup,
    getArtistsByEmail,
    getArtist,
    linkArtistToUser,
  }
}
