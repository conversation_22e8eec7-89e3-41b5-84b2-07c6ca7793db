import { ref } from 'vue'
import type { Ref } from 'vue'
import { doc, getDoc, updateDoc, Timestamp } from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { Artist } from '@/models/Artist'

export function useArtist(artistId: string) {
  const { db } = useFirebase()
  const artist: Ref<Artist | null> = ref(null)
  const isLoadingArtist = ref(true)
  const error: Ref<string | null> = ref(null)

  const fetchArtist = async () => {
    try {
      isLoadingArtist.value = true
      error.value = null

      const docRef = doc(db, 'artists', artistId)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        artist.value = Artist.fromFirestore({
          id: docSnap.id,
          data: () => docSnap.data(),
        })
      } else {
        error.value = 'Artist not found'
      }
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error loading artist:', e)
      }
    } finally {
      isLoadingArtist.value = false
    }
  }

  const updateArtist = async (updates: Partial<Artist>) => {
    try {
      const docRef = doc(db, 'artists', artistId)
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now(),
      })

      // Update local state
      if (artist.value) {
        Object.assign(artist.value, updates, {
          updatedAt: Timestamp.now(),
        })
      }
      return true
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error updating artist:', e)
      }
      return false
    }
  }

  fetchArtist()

  return {
    artist,
    isLoadingArtist,
    error,
    fetchArtist,
    updateArtist,
  }
}
