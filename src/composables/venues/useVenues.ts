import { ref, getCurrentInstance, onUnmounted } from 'vue'
import {
  collection,
  getDocs,
  doc,
  getDoc,
  query,
  where,
  onSnapshot,
  setDoc,
  deleteDoc,
  type Unsubscribe,
} from 'firebase/firestore'
import { useToast } from 'vue-toastification'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { useEvents } from '@/composables/events/useEvents'
import { useConfirmationStore } from '@/stores/confirmationStore'
import { useMessageStore } from '@/stores/messageStore'
import { Venue } from '@/models/Venue'
import { Event } from '@/models/Event'
import { Act } from '@/models/Act'

const confirmationStore = useConfirmationStore()
const messageStore = useMessageStore()

export function useVenues() {
  const { db, auth } = useFirebase()
  const venues = ref<Venue[]>([])
  const isLoadingVenues = ref(true)
  const error = ref<string | null>(null)
  const toast = useToast()
  let unsubscribeVenues: Unsubscribe | null = null

  const generateVenueId = (venue: Partial<Venue>): string => {
    if (!venue.name || !venue.address?.town) {
      throw new Error('Venue name and town are required for ID generation')
    }

    let id = venue.name
    if (!venue.name.includes(venue.address.town)) {
      id += `-${venue.address.town}`
    }

    return id
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-') // Replace non-alphanumeric chars with hyphens
      .replace(/-+/g, '-') // Replace multiple consecutive hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
  }

  const createVenue = async (venueData: Partial<Venue>): Promise<string> => {
    try {
      const id = generateVenueId(venueData)
      const venue = new Venue(venueData)
      const cleanData = venue.toFirestore()

      await setDoc(doc(db, 'venues', id), {
        ...cleanData,
        createdAt: new Date(),
        updatedAt: null,
      })

      return id
    } catch (e) {
      const errorMessage =
        e instanceof Error ? e.message : 'Failed to create venue'
      error.value = errorMessage
      throw new Error(errorMessage)
    }
  }

  const subscribeToVenues = () => {
    cleanupVenues()
    isLoadingVenues.value = true
    error.value = null

    try {
      const venuesRef = collection(db, 'venues')
      unsubscribeVenues = onSnapshot(
        venuesRef,
        snapshot => {
          const fetchedVenues: Venue[] = []
          snapshot.forEach(doc => {
            const venue = Venue.fromFirestore(doc)
            if (venue) {
              fetchedVenues.push(venue)
            } else {
              console.warn(
                `Venue document ${doc.id} could not be converted to Venue model`,
              )
            }
          })
          venues.value = fetchedVenues.sort((a, b) =>
            a.getName().localeCompare(b.getName()),
          )
          isLoadingVenues.value = false
        },
        err => {
          console.error('Error subscribing to venues:', err)
          error.value = err.message
          isLoadingVenues.value = false
        },
      )
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error setting up venues subscription:', e.message)
      } else {
        error.value = 'An unknown error occurred'
        console.error('Error setting up venues subscription:', e)
      }
      isLoadingVenues.value = false
    }
  }

  // Add these new refs for single venue subscription
  const currentVenue = ref<Venue | null>(null)
  const isLoadingVenue = ref(false)
  let unsubscribeCurrentVenue: Unsubscribe | null = null

  const cleanupCurrentVenue = () => {
    if (unsubscribeCurrentVenue) {
      unsubscribeCurrentVenue()
      unsubscribeCurrentVenue = null
    }
    currentVenue.value = null
  }

  const fetchVenue: (id: string) => Promise<Venue | null> = async (
    id: string,
  ): Promise<Venue | null> => {
    try {
      error.value = null
      const docRef = doc(db, 'venues', id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const venue = Venue.fromFirestore(docSnap)
        if (venue) {
          return venue
        }
      }
      error.value = 'Venue not found'
      return null
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error loading venue:', e.message)
      } else {
        error.value = 'An unknown error occurred'
        console.error('Error loading venue:', e)
      }
      return null
    }
  }

  // Rename the existing method to be more specific
  function subscribeEventToVenue(event: Event) {
    if (!event.venue) return

    const unsubscribe = onSnapshot(
      doc(db, 'venues', event.venue),
      snapshot => {
        if (snapshot.exists()) {
          const venueData = { id: snapshot.id, ...snapshot.data() } as Venue
          event.venueDetails = venueData
        }
      },
      error => console.error('Venue subscription error:', error),
    )

    event.unsubscribes.add(unsubscribe)
  }

  // New method for subscribing to a single venue
  const subscribeToVenue = (venueId: string) => {
    // Clean up any existing subscription
    cleanupCurrentVenue()

    isLoadingVenue.value = true
    error.value = null

    try {
      unsubscribeCurrentVenue = onSnapshot(
        doc(db, 'venues', venueId),
        snapshot => {
          if (snapshot.exists()) {
            const venue = Venue.fromFirestore(snapshot)
            if (venue) {
              currentVenue.value = venue
            } else {
              console.warn(
                `Venue document ${venueId} could not be converted to Venue model`,
              )
              currentVenue.value = null
            }
          } else {
            currentVenue.value = null
            error.value = 'Venue not found'
          }
          isLoadingVenue.value = false
        },
        err => {
          console.error('Error subscribing to venue:', err)
          error.value = err.message
          isLoadingVenue.value = false
          currentVenue.value = null
        },
      )
    } catch (e: unknown) {
      if (e instanceof Error) {
        error.value = e.message
        console.error('Error setting up venue subscription:', e.message)
      } else {
        error.value = 'An unknown error occurred'
        console.error('Error setting up venue subscription:', e)
      }
      isLoadingVenue.value = false
      currentVenue.value = null
    }
  }

  // Update the cleanup function to include current venue cleanup
  const cleanupVenues = () => {
    if (unsubscribeVenues) {
      unsubscribeVenues()
      unsubscribeVenues = null
    }
    cleanupCurrentVenue()
  }

  const fetchVenueEvents = async (venueId: string): Promise<Event[]> => {
    try {
      const { updateEvent } = useEvents()
      const eventsRef = collection(db, 'events')
      const q = query(eventsRef, where('venue', '==', venueId))
      const querySnapshot = await getDocs(q)

      const loadedEvents = querySnapshot.docs.map(doc => {
        const event = new Event({
          id: doc.id,
          ...doc.data(),
        } as Event & { id: string })

        event.setSaveCallback(async (id, updates) => {
          await updateEvent(id, updates)
        })

        // Use the renamed method
        subscribeEventToVenue(event)
        subscribeToActs(event)

        return event
      })

      // Wait for all events to be fully loaded
      await Promise.all(
        loadedEvents.map(
          event =>
            new Promise<void>(resolve => {
              const checkLoaded = setInterval(() => {
                if (event.isFullyLoaded) {
                  clearInterval(checkLoaded)
                  resolve()
                }
              }, 100)
            }),
        ),
      )

      return loadedEvents.sort((a, b) => a.when.seconds - b.when.seconds)
    } catch (e: unknown) {
      if (e instanceof Error) {
        console.error('Error loading venue events:', e.message)
      } else {
        console.error('Error loading venue events:', e)
      }
      return []
    }
  }

  // Update the subscribeToActs function to use the renamed method
  function subscribeToActs(event: Event) {
    event.acts.forEach(actId => {
      const unsubscribe = onSnapshot(
        doc(db, 'acts', actId),
        snapshot => {
          if (snapshot.exists()) {
            const actData = { id: snapshot.id, ...snapshot.data() } as Act
            const actIndex = event.actDetails.findIndex(a => a.id === actId)
            if (actIndex >= 0) {
              event.actDetails[actIndex] = actData
            } else {
              event.actDetails.push(actData)
            }
          }
        },
        error => console.error('Act subscription error:', error),
      )

      event.unsubscribes.add(unsubscribe)
    })
  }

  // Start subscription when composable is initialized
  subscribeToVenues()

  // Cleanup on component unmount
  if (typeof window !== 'undefined') {
    const currentInstance = getCurrentInstance()
    if (currentInstance) {
      onUnmounted(cleanupVenues)
    }
  }

  async function deleteVenue(venueId: string) {
    const confirmed = await new Promise<boolean>(resolve => {
      confirmationStore.showConfirmation({
        title: 'Delete Venue',
        message:
          'Are you sure you want to delete this venue? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false),
      })
    })

    if (!confirmed) return false

    try {
      await deleteDoc(doc(db, 'venues', venueId))
      toast.success('Venue deleted successfully')
      return true
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete venue'
      error.value = errorMessage
      toast.error(errorMessage)
      return false
    }
  }

  return {
    venues,
    isLoadingVenues,
    currentVenue, // New: for single venue subscription
    isLoadingVenue, // New: loading state for single venue
    error,
    createVenue,
    fetchVenue,
    fetchVenueEvents,
    subscribeToVenues,
    subscribeToVenue, // New: subscribe to single venue
    cleanupVenues,
    deleteVenue,
    currentUser: auth.currentUser,
  }
}
