import { ref, type Ref } from 'vue'
import {
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
} from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { handleNestedFieldUpdate } from '@/firebase/utils'

interface VenueData {
  id: string
  name?: string
  address?: string
  capacity?: number
  contactInfo?: {
    email?: string
    phone?: string
  }
  createdAt?: Date
  updatedAt?: Date
  [key: string]: any
}

export function useVenue(venueId: string) {
  const { db } = useFirebase()
  const venue: Ref<VenueData | null> = ref(null)
  const isLoadingVenue = ref(true)
  const error: Ref<string | null> = ref(null)

  const fetchVenue = async (): Promise<void> => {
    try {
      isLoadingVenue.value = true
      error.value = null

      const docRef = doc(db, 'venues', venueId)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        venue.value = {
          id: docSnap.id,
          ...docSnap.data(),
        }
      } else {
        error.value = 'Venue not found'
      }
    } catch (e: unknown) {
      error.value = e instanceof Error ? e.message : 'Unknown error occurred'
      console.error('Error loading venue:', e)
    } finally {
      isLoadingVenue.value = false
    }
  }

  const updateVenue = async (field: string, value: any): Promise<boolean> => {
    try {
      const docRef = doc(db, 'venues', venueId)
      const updateData = {
        ...handleNestedFieldUpdate(field, value),
        updatedAt: Timestamp.now(),
      }

      await updateDoc(docRef, updateData)

      // Update local state
      if (venue.value) {
        if (field.includes('.')) {
          const [parent, child] = field.split('.')
          venue.value[parent] = {
            ...venue.value[parent],
            [child]: value,
          }
        } else {
          venue.value[field] = value
        }
        venue.value.updatedAt = new Date()
      }

      return true
    } catch (e: unknown) {
      error.value = e instanceof Error ? e.message : 'Unknown error occurred'
      console.error('Error updating venue:', e)
      return false
    }
  }

  const deleteVenue = async (): Promise<boolean> => {
    try {
      await deleteDoc(doc(db, 'venues', venueId))
      return true
    } catch (e: unknown) {
      error.value = e instanceof Error ? e.message : 'Unknown error occurred'
      console.error('Error deleting venue:', e)
      return false
    }
  }

  // Fetch venue data on creation
  fetchVenue()

  return {
    venue,
    isLoadingVenue,
    error,
    fetchVenue,
    updateVenue,
    deleteVenue,
  }
}
