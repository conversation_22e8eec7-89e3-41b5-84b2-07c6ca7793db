import { ref } from 'vue'
import {
  collection,
  query,
  where,
  orderBy,
  doc,
  Timestamp,
  onSnapshot,
  type Query,
  type CollectionReference,
  type Unsubscribe,
  deleteDoc,
  setDoc,
  updateDoc,
  getDocs,
  limit,
} from 'firebase/firestore'
import { useFirebase } from '@/composables/firebase/useFirebase'
import { Event } from '@/models/Event'
import type { EventStatus } from '@/types/models'
import type { Venue } from '@/models/Venue'
import type { Act } from '@/models/Act'
import { useConfirmationStore } from '@/stores/confirmationStore'
import { useToast } from 'vue-toastification'

interface EventFilters {
  status?: EventStatus
  fromDate?: Date
  toDate?: Date
  sortDescending?: boolean
  includePaid?: boolean
}

export function useEvents(defaultFilters?: Partial<EventFilters>) {
  const { db } = useFirebase()
  const events = ref<Event[]>([])
  const currentEvent = ref<Event | undefined>(undefined)
  const isLoading = ref(true)
  const areDetailsLoading = ref(false)
  const error = ref<string | null>(null)
  const confirmationStore = useConfirmationStore()
  const toast = useToast()
  const subscriptions = new Set<Unsubscribe>()

  // Initialize subscription if defaultFilters provided
  if (defaultFilters) {
    subscribeToEvents(defaultFilters)
  }

  // Cleanup function to be called by components
  function cleanup() {
    subscriptions.forEach(unsubscribe => unsubscribe())
    subscriptions.clear()
    events.value = []
    currentEvent.value = undefined
    error.value = null
  }

  // Helper to build query based on filters
  function buildEventsQuery(filters: Partial<EventFilters> = {}): Query {
    let eventsQuery: Query | CollectionReference = collection(db, 'events')

    if (filters.status) {
      eventsQuery = query(eventsQuery, where('status', '==', filters.status))
    }

    if (filters.fromDate) {
      eventsQuery = query(
        eventsQuery,
        where('when', '>=', Timestamp.fromDate(filters.fromDate)),
      )
    }

    if (filters.toDate) {
      eventsQuery = query(
        eventsQuery,
        where('when', '<=', Timestamp.fromDate(filters.toDate)),
      )
    }

    if (!filters.includePaid) {
      eventsQuery = query(
        eventsQuery,
        where('notes.fee.paid', 'in', [false, null]),
      )
    }

    return query(
      eventsQuery,
      orderBy('when', filters.sortDescending ? 'desc' : 'asc'),
    )
  }

  // Subscribe to venue updates for an event
  function subscribeToVenue(event: Event) {
    if (!event.venue) return

    const unsubscribe = onSnapshot(
      doc(db, 'venues', event.venue),
      snapshot => {
        if (snapshot.exists()) {
          const venueData = { id: snapshot.id, ...snapshot.data() } as Venue
          event.venueDetails = venueData
          event.venueCoords =
            event.venueCoords || event.venueDetails.address.coords || ''
        }
      },
      error => console.error('Venue subscription error:', error),
    )

    subscriptions.add(unsubscribe)
  }

  // Subscribe to act updates for an event
  function subscribeToActs(event: Event) {
    event.acts.forEach(actId => {
      const unsubscribe = onSnapshot(
        doc(db, 'acts', actId),
        snapshot => {
          if (snapshot.exists()) {
            const actData = { id: snapshot.id, ...snapshot.data() } as Act
            const actIndex = event.actDetails.findIndex(a => a.id === actId)
            if (actIndex >= 0) {
              event.actDetails[actIndex] = actData
            } else {
              event.actDetails.push(actData)
            }
          }
        },
        error => console.error('Act subscription error:', error),
      )

      subscriptions.add(unsubscribe)
    })
  }

  // Subscribe to multiple events or a single event
  async function subscribeToEvents(filters: Partial<EventFilters> = {}) {
    cleanup()
    isLoading.value = true
    areDetailsLoading.value = true
    error.value = null

    try {
      const eventsQuery = buildEventsQuery(filters)

      const unsubscribe = onSnapshot(eventsQuery, async snapshot => {
        const loadedEvents = snapshot.docs
          .map(doc => {
            try {
              const event = new Event({ id: doc.id, ...doc.data() } as Event & {
                id: string
              })
              return event
            } catch (err) {
              toast.error(
                `Failed to load event ${doc.id}. Please contact support.`,
              )
              return null
            }
          })
          .filter((event): event is Event => event !== null)

        // Subscribe to details after mapping
        loadedEvents.forEach(event => {
          try {
            subscribeToVenue(event)
            subscribeToActs(event)
          } catch (err) {
            console.error(
              `Failed to subscribe to details for ${event.id}:`,
              err,
            )
          }
        })

        try {
          await Promise.all(
            loadedEvents.map(
              event =>
                new Promise<void>(resolve => {
                  const startTime = Date.now()
                  const checkLoaded = setInterval(() => {
                    if (event.isFullyLoaded) {
                      clearInterval(checkLoaded)
                      resolve()
                    } else if (Date.now() - startTime > 5000) {
                      clearInterval(checkLoaded)
                      console.warn(
                        `Timeout waiting for event ${event.id} to load`,
                      )
                      resolve()
                    }
                  }, 100)
                }),
            ),
          )

          events.value = loadedEvents
        } catch (err) {
          console.error('Error waiting for events to load:', err)
        }

        isLoading.value = false
        areDetailsLoading.value = false
      })

      subscriptions.add(unsubscribe)
    } catch (err) {
      console.error('Error in subscribeToEvents:', err)
      error.value = err instanceof Error ? err.message : 'Failed to load events'
      isLoading.value = false
      areDetailsLoading.value = false
    }
  }

  // Subscribe to a single event
  const subscribeToEvent = async (eventId: string) => {
    cleanup()
    isLoading.value = true // Set initial loading state
    areDetailsLoading.value = true
    error.value = null

    try {
      const unsubscribe = onSnapshot(
        doc(db, 'events', eventId),
        async snapshot => {
          isLoading.value = false // Turn off main loading after initial snapshot

          if (!snapshot.exists()) {
            error.value = 'Event not found'
            currentEvent.value = undefined
            areDetailsLoading.value = false
            return
          }

          const event = new Event({
            id: snapshot.id,
            ...snapshot.data(),
          } as Event & { id: string })

          event.setSaveCallback(async (id, updates) => {
            await updateEvent(id, updates)
          })

          subscribeToVenue(event)
          subscribeToActs(event)

          // Wait for event to be fully loaded
          await new Promise<void>(resolve => {
            const checkLoaded = setInterval(() => {
              if (event.isFullyLoaded) {
                clearInterval(checkLoaded)
                resolve()
              }
            }, 100)
          })

          currentEvent.value = event
          areDetailsLoading.value = false // Turn off details loading after everything is loaded
        },
        err => {
          console.error('Error in event subscription:', err)
          isLoading.value = false
          areDetailsLoading.value = false
          error.value = 'Failed to load event'
        },
      )

      subscriptions.add(unsubscribe)
    } catch (err) {
      console.error('Error in subscribeToEvent:', err)
      isLoading.value = false
      areDetailsLoading.value = false
      error.value = 'Failed to load event'
    }
  }

  // Create a new event
  async function createEvent(eventData: Partial<Event>) {
    try {
      const id = generateEventId(eventData)

      // Remove runtime-only properties before saving
      const cleanData = { ...eventData }
      delete (cleanData as any).venueDetails
      delete (cleanData as any).actDetails
      delete (cleanData as any)._detailsLoaded
      delete (cleanData as any).unsubscribes

      await setDoc(doc(db, 'events', id), {
        ...cleanData,
        createdAt: Timestamp.now(),
        updatedAt: null,
      })
      return id
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to create event'
      throw err
    }
  }

  // Update an event
  async function updateEvent(eventId: string, updates: Partial<Event>) {
    try {
      const docRef = doc(db, 'events', eventId)

      // Create a clean version of the updates
      const cleanUpdates = {
        ...updates,
        updatedAt: Timestamp.now(),
      }

      // Remove runtime-only fields
      const fieldsToRemove = [
        'unsubscribes',
        'venueDetails',
        'actDetails',
        '_detailsLoaded',
        '_status',
        '_saveCallback',
        'saveCallback',
      ]

      fieldsToRemove.forEach(field => {
        delete (cleanUpdates as any)[field]
      })

      // Ensure we're using the public status if it's being updated
      if ('_status' in updates) {
        cleanUpdates.status = updates._status as EventStatus
      }

      await updateDoc(docRef, cleanUpdates)
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to update event'
      throw err
    }
  }

  async function deleteEvent(eventId: string) {
    const confirmed = await new Promise<boolean>(resolve => {
      confirmationStore.showConfirmation({
        title: 'Delete Event',
        message: 'Are you sure you want to delete this event?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false),
        type: 'warning',
      })
    })

    if (!confirmed) return false

    try {
      await deleteDoc(doc(db, 'events', eventId))
      toast.success('Event deleted successfully')
      return true
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete event'
      error.value = errorMessage
      toast.error(errorMessage)
      return false
    }
  }

  // Generate event ID from event data
  function generateEventId(eventData: Partial<Event>): string {
    const date = eventData.when?.toDate().toISOString().split('T')[0] || ''
    const venue = eventData.venue || ''
    const acts = eventData.acts?.join('-') || ''

    return `${date}-${venue}-${acts}`
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  // Add this function to the useEvents composable
  function getEventsForDay(date: Date) {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    return events.value.filter(event => {
      const eventDate =
        event.when instanceof Date ? event.when : event.when.toDate()
      return eventDate >= startOfDay && eventDate <= endOfDay
    })
  }

  const getEventsDateRange = async () => {
    try {
      const range = {
        // start needs to be the first event (by date) in the whole database
        start: (
          await getDocs(
            query(collection(db, 'events'), orderBy('when', 'asc'), limit(1)),
          )
        ).docs[0]
          .data()
          .when.toDate(),
        // end needs to be the last event (by date) in the whole database
        end: (
          await getDocs(
            query(collection(db, 'events'), orderBy('when', 'desc'), limit(1)),
          )
        ).docs[0]
          .data()
          .when.toDate(),
        years() {
          return [this.start.getFullYear(), this.end.getFullYear()]
        },
      }

      return range
    } catch (err) {
      console.error('Error getting events date range:', err)
      return null // Only return null if there's an actual error
    }
  }

  return {
    // State
    events,
    currentEvent,
    isLoading,
    areDetailsLoading,
    error,

    // Methods
    subscribeToEvents,
    subscribeToEvent,
    createEvent,
    updateEvent,
    deleteEvent,
    cleanup,
    getEventsForDay, // Add this to the returned object
    getEventsDateRange,
  }
}
