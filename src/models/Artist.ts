import { Timestamp } from 'firebase/firestore'

type ArtistPhoto = {
  url?: string
  publicId?: string
}

type ArtistAvailability = {
  id?: string
  date: string
  type: 'unavailable' | 'tentative' | 'available'
  reason?: string
  notes?: string
  createdAt?: Timestamp
}

export class Artist {
  id!: string
  firstName!: string
  lastName!: string
  stageName!: string
  instruments!: string[]
  photoId: string | null = null
  availability: ArtistAvailability[] = []
  createdAt!: Timestamp
  updatedAt: Timestamp | null = null

  constructor(data: Artist & { id: string }) {
    if (!data) throw new Error('Artist data is required')
    if (!data.id) throw new Error('Artist ID is required')

    // Normalize the input data with defaults
    const normalizedData = {
      id: data.id,
      firstName: data.firstName || '',
      lastName: data.lastName || '',
      stageName: data.stageName || '',
      instruments: data.instruments || [],
      photoId: data.photoId || null,
      availability: data.availability || [],
      createdAt: data.createdAt || Timestamp.now(),
      updatedAt: data.updatedAt || null,
    }

    // Assign normalized data to properties
    Object.assign(this, normalizedData)
  }

  get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim()
  }

  get displayName(): string {
    return this.stageName || this.fullName
  }

  get initial(): string {
    return this.displayName.charAt(0).toUpperCase()
  }

  get initials(): string {
    return (this.firstName.charAt(0) + this.lastName.charAt(0)).toUpperCase()
  }

  get currentAvailability(): ArtistAvailability | undefined {
    return this.availability
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .find(a => new Date(a.date) >= new Date())
  }

  get unavailabilityReason(): string | null {
    const current = this.currentAvailability
    return current?.type === 'unavailable' ? current.reason || null : null
  }

  setAvailability(availability: ArtistAvailability): void {
    const index = this.availability.findIndex(a => a.date === availability.date)
    if (index > -1) {
      this.availability[index] = availability
    } else {
      this.availability.push({
        ...availability,
        createdAt: Timestamp.now(),
      })
    }
  }

  toFirestore(): Partial<Artist> {
    return {
      firstName: this.firstName,
      lastName: this.lastName,
      stageName: this.stageName,
      instruments: this.instruments,
      photoId: this.photoId,
      availability: this.availability,
      createdAt: this.createdAt,
      updatedAt: Timestamp.now(),
    }
  }

  static fromFirestore(doc: { id: string; data(): any }): Artist {
    if (!doc.id) {
      throw new Error('Artist document must have an ID')
    }

    return new Artist({
      id: doc.id,
      ...doc.data(),
    })
  }
}
