// Firebase imports
import { Timestamp } from 'firebase/firestore'
import { dateToTimestamp, timestampToDate } from '@/firebase/utils'

// Composable imports
import { useDateTime } from '@/composables/useDateTime.ts'

// Model imports
import { Venue } from '@/models/Venue.ts'
import { Act } from '@/models/Act.ts'

// Type imports
import type { EventStatus } from '@/types/models'
import type { Unsubscribe } from 'firebase/firestore'

// Type definitions
export type PaymentSchema = {
  amount?: number | null
  paid?: boolean
  date?: Timestamp | null
}

export type EventStatusHistoryEntry = {
  status: EventStatus
  date: Timestamp
  note?: string
}

export type EventNotes = {
  fee: PaymentSchema
  deposit: PaymentSchema
  agent: string | null
}

// Event class
export class Event {
  id!: string
  title?: string | null
  description?: string
  when!: Timestamp
  duration!: number
  venue!: string // Just the ID
  venueCoords?: string
  acts!: string[] // Just the IDs
  cta!: {
    type: 'fb' | 'ticket'
    text: string
    url: string
    price?: string | null
  } | null
  notes!: EventNotes
  isPrivate!: boolean
  private _status!: EventStatus
  createdAt!: Timestamp
  updatedAt!: Timestamp | null
  statusHistory: EventStatusHistoryEntry[] = []

  // Runtime-only properties (not stored in Firestore)
  venueDetails: Venue | null = null
  actDetails: Act[] = []
  private _detailsLoaded: boolean = false
  unsubscribes: Set<Unsubscribe> = new Set()

  private static getDefaultNotes(): EventNotes {
    return {
      fee: {
        amount: null,
        date: null,
        paid: false,
      },
      deposit: {
        amount: null,
        date: null,
        paid: false,
      },
      agent: null,
    }
  }

  constructor(data: Event & { id: string }) {
    if (!data) throw new Error('Event data is required')
    if (!data.id) throw new Error('Event ID is required')

    // First set the status directly to the private field
    this._status = data.status || 'draft'

    // Normalize the input data with defaults
    const normalizedData = {
      id: data.id,
      title: data.title || null,
      description: data.description || '',
      when: data.when,
      duration: data.duration || 0,
      venue: data.venue || '',
      venueCoords: data.venueCoords || null,
      acts: data.acts || [],
      cta: data.cta || null,
      notes: {
        ...Event.getDefaultNotes(),
        ...data.notes,
      },
      isPrivate: data.isPrivate ?? false,
      // Remove status from normalizedData since we handle it separately
      statusHistory: (data.statusHistory || []).map(entry => ({
        status: entry.status,
        date: entry.date,
        note: entry.note || '',
      })),
      venueDetails: null,
      actDetails: [],
      createdAt: data.createdAt,
      updatedAt: data.updatedAt || null,
    }

    // Assign normalized data to properties
    // This will set the status to the private field
    Object.assign(this, normalizedData)
  }

  // Venue-related methods
  venueName(): string {
    return this.venueDetails?.name || 'Unnamed Venue'
  }

  venueAddress(): string {
    if (!this.venueDetails?.address) return ''

    const { town, county, postcode } = this.venueDetails.address
    return [town, county, postcode].filter(Boolean).join(', ')
  }

  getVenueCoords(): string {
    return this.venueCoords || this.venueDetails?.address?.coords || ''
  }

  venueContactInfo(): string {
    if (!this.venueDetails) return ''
    const contact = this.venueDetails.getContactInfo()
    return `${contact.phone} ${contact.email} ${contact.website}`.trim()
  }

  // Display methods
  fullTitle(): string {
    if (this.title) return this.title

    // Generate a title based on acts if no title is set
    return this.acts.length
      ? `${this.actNames()} @ ${this.venueName()}`
      : 'Untitled Event'
  }

  shortTitle(): string {
    if (this.title) return this.title

    return this.acts.length
      ? `${this.actDisplayNames()} @ ${this.venueName()}`
      : 'Untitled Event'
  }

  actNames(): string {
    return (
      this.actDetails
        .map(act => act.name)
        .filter(Boolean)
        .join(', ') || 'Unknown Acts'
    )
  }

  actDisplayNames(): string {
    return (
      this.actDetails
        .map(act => act.displayName)
        .filter(Boolean)
        .join('/') || 'Unknown Acts'
    )
  }

  startTime(): string {
    const { formatTime } = useDateTime()
    return formatTime(this.when)
  }

  endTime(): Date {
    const { getEndTime } = useDateTime()
    return new Date(getEndTime(this.when, this.duration))
  }

  formattedDate(): string {
    const { formatDate } = useDateTime()
    return formatDate(this.when.toDate())
  }

  // Status methods
  isPast(): boolean {
    return this.when.toDate() < new Date()
  }

  get status(): EventStatus {
    return this._status
  }

  set status(newStatus: EventStatus) {
    this._status = newStatus

    // Add to status history
    this.statusHistory.push({
      status: newStatus,
      date: Timestamp.now(),
    })
  }

  private async save(updates?: Partial<Event>) {
    if (!this.saveCallback) {
      throw new Error('No save callback provided to Event instance')
    }

    // If updates are provided, use them, otherwise create default updates
    const updateData = updates || {
      notes: this.notes,
      when: this.when,
      status: this.status,
      statusHistory: this.statusHistory,
      updatedAt: Timestamp.now(),
    }

    // Remove runtime-only properties before saving
    const cleanUpdates = { ...updateData }
    delete (cleanUpdates as any).venueDetails
    delete (cleanUpdates as any).actDetails
    delete (cleanUpdates as any)._detailsLoaded
    delete (cleanUpdates as any).unsubscribes

    await this.saveCallback(this.id, cleanUpdates)
  }

  // Add a method to set the save callback
  private saveCallback?: (id: string, updates: Partial<Event>) => Promise<void>

  setSaveCallback(
    callback: (id: string, updates: Partial<Event>) => Promise<void>,
  ) {
    this.saveCallback = callback
  }

  get date(): Date {
    return timestampToDate(this.when) ?? new Date()
  }

  async setDate(date: Date) {
    const { getNextAvailable8PM } = useDateTime()
    this.when =
      dateToTimestamp(date) ?? Timestamp.fromDate(getNextAvailable8PM())
    await this.save()
  }

  get createdAtDate(): Date {
    return timestampToDate(this.createdAt) ?? new Date()
  }

  get updatedAtDate(): Date {
    return timestampToDate(this.updatedAt) ?? new Date()
  }

  // Financial methods
  fee(): number {
    return this.notes?.fee?.amount || 0
  }

  isFeePaid(): boolean {
    return this.notes?.fee?.paid || false
  }

  feePaymentDate(): Date | null {
    return timestampToDate(this.notes?.fee?.date) || null
  }

  deposit(): number {
    return this.notes?.deposit?.amount || 0
  }

  isDepositPaid(): boolean {
    return this.notes?.deposit?.paid || false
  }

  depositPaymentDate(): Date | null {
    return timestampToDate(this.notes?.deposit?.date) || null
  }

  outstandingBalance(): number {
    if (!this.fee()) return 0

    if (!this.deposit() || !this.isDepositPaid()) return this.fee()

    return this.fee() - this.deposit()
  }

  agentId(): string {
    return this.notes?.agent || ''
  }

  async setPaymentDetails(
    paymentType: 'fee' | 'deposit',
    paymentDetails: PaymentSchema,
  ) {
    this.notes[paymentType] = {
      amount: paymentDetails?.amount ?? this.notes[paymentType]?.amount ?? null,
      paid: paymentDetails?.paid ?? this.notes[paymentType]?.paid ?? false,
      date:
        paymentDetails?.paid && paymentDetails?.date
          ? paymentDetails.date
          : (this.notes[paymentType]?.date ?? null),
    }
    await this.save()
  }

  getAct(actId: string): Act | null {
    return this.actDetails?.find(act => act.id === actId) || null
  }

  getActBadgeUrl(actId: string): string | null {
    const act = this.getAct(actId)
    return act?.logoUrls?.badge || null
  }

  get isFullyLoaded(): boolean {
    const hasVenueDetails = this.venueDetails !== null
    const hasAllActDetails = this.acts.length === this.actDetails.length

    return hasVenueDetails && hasAllActDetails
  }

  /**
   * Performs a fuzzy search across venue details, matching against multiple fields
   * Returns true if all space-separated terms match any venue field
   */
  matchesVenueSearch(searchQuery: string): boolean {
    if (!this.venueDetails) return false
    if (!searchQuery.trim()) return true

    // Get all search terms (split by spaces and filter out empty strings)
    const searchTerms = searchQuery.toLowerCase().split(/\s+/).filter(Boolean)

    // Fields to search in
    const searchFields = [
      this.venueDetails.name,
      this.venueDetails.address?.address1,
      this.venueDetails.address?.address2,
      this.venueDetails.address?.town,
      this.venueDetails.address?.county,
      this.venueDetails.address?.postcode,
    ]
      .filter(Boolean)
      .map(field => field.toLowerCase())

    // Check if all search terms match at least one field
    return searchTerms.every(term =>
      searchFields.some(field => field.includes(term)),
    )
  }
}
