import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { autoAnimatePlugin } from '@formkit/auto-animate/vue'
import { baseComponents } from '@/plugins/baseComponents.ts'
import Toast, { toastOptions } from '@/plugins/toast'

import App from '@/App.vue'
import router from '@/router/index'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(autoAnimatePlugin)
app.use(baseComponents)
app.use(Toast, toastOptions)

app.mount('#app')
